"""
管理LangChain智能体路由

功能：
1. 智能体创建、更新、删除
2. 智能体配置管理
3. 用户智能体分配管理
4. 智能体对话测试
5. 智能体统计信息
6. 模型配置管理
7. LangChain知识库管理
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from fastapi import APIRouter, Body, Depends, File, Form, Path, UploadFile
from pydantic import BaseModel, Field, field_validator

from 依赖项.认证 import 获取当前管理员用户
from 数据.LangChain_工具数据层 import LangChain工具数据层实例
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器 as 路由日志器
from 日志 import 错误日志器
from 状态 import 状态
from 服务.LangChain_内部函数包装器 import 内部函数包装器实例
from 服务.LangChain_工具管理器 import LangChain工具管理器实例

# 使用统一的智能体服务
from 服务.LangChain_智能体服务 import LangChain智能体服务实例
from 服务.LangChain_模型管理器 import LangChain模型管理器实例
from 服务.LangChain_知识库服务 import LangChain知识库服务实例
from 服务.LangChain_结构化输出处理器 import LangChain结构化输出处理器实例

# 创建路由器
管理LangChain智能体路由 = APIRouter(tags=["管理-LangChain智能体"])


# ==================== 请求模型定义 ====================


class 管理员创建智能体请求模型(BaseModel):
    """管理员创建智能体请求模型"""

    智能体名称: str = Field(..., description="智能体名称", min_length=1, max_length=100)
    智能体描述: Optional[str] = Field(None, description="智能体描述", max_length=500)

    模型名称: str = Field("qwen-turbo", description="AI模型名称")
    langchain_模型配置表id: Optional[int] = Field(
        None, description="LangChain模型配置表id"
    )
    系统提示词: str = Field(..., description="系统提示词", min_length=1)
    用户提示词: Optional[str] = Field(None, description="用户提示词")
    角色设定: Optional[str] = Field(None, description="角色设定")
    行为规范: Optional[str] = Field(None, description="行为规范")
    温度参数: float = Field(0.7, description="生成温度参数", ge=0.0, le=2.0)
    最大令牌数: int = Field(4000, description="最大令牌数", ge=1, le=32000)
    记忆窗口大小: int = Field(10, description="记忆窗口大小", ge=1, le=50)
    启用rag: bool = Field(False, description="是否启用rag")
    知识库列表: Optional[List[int]] = Field([], description="关联知识库列表")
    输出格式: str = Field("text", description="输出格式")
    是否公开: Union[bool, int] = Field(
        1, description="是否为公开智能体（支持 true/false 或 1/0）"
    )
    是否启用: bool = Field(True, description="是否启用智能体")
    标签: Optional[List[str]] = Field([], description="智能体标签")
    # 新的三字段数据结构
    自定义回复格式: Optional[Dict[str, Any]] = Field(
        None, description="自定义回复格式配置"
    )
    rag_配置: Optional[Dict[str, Any]] = Field(None, description="RAG检索配置")
    自定义变量: Optional[List[Dict[str, Any]]] = Field([], description="自定义变量列表")
    # 兼容字段 - 用于前端传递RAG相关参数
    检索策略: Optional[str] = Field(None, description="检索策略")
    嵌入模型id: Optional[int] = Field(None, description="嵌入模型id")
    相似度阈值: Optional[float] = Field(None, description="相似度阈值")
    最大检索数量: Optional[int] = Field(None, description="最大检索数量")
    # JSON结构化输出相关字段
    json_schema: Optional[Dict[str, Any]] = Field(
        None, description="JSON结构化输出配置"
    )
    pydantic_输出模式: Optional[str] = Field(None, description="Pydantic输出模式")


class 智能体列表查询请求模型(BaseModel):
    """智能体列表查询请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)

    状态: Optional[str] = Field(None, description="状态过滤")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    是否公开: Optional[bool] = Field(None, description="是否公开过滤")


class 用户分配查询请求模型(BaseModel):
    """用户分配查询请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    用户id: Optional[int] = Field(None, description="用户id过滤")
    智能体id: Optional[int] = Field(None, description="智能体id过滤")
    状态: Optional[str] = Field(None, description="关联状态过滤")


class 更新智能体请求模型(BaseModel):
    """更新智能体请求模型"""

    智能体id: int = Field(..., description="智能体id")
    智能体名称: Optional[str] = Field(None, description="智能体名称", max_length=100)
    智能体描述: Optional[str] = Field(None, description="智能体描述", max_length=500)

    模型名称: Optional[str] = Field(None, description="AI模型名称")
    langchain_模型配置表id: Optional[int] = Field(
        None, description="LangChain模型配置表id"
    )
    系统提示词: Optional[str] = Field(None, description="系统提示词")
    用户提示词: Optional[str] = Field(None, description="用户提示词")
    角色设定: Optional[str] = Field(None, description="角色设定")
    行为规范: Optional[str] = Field(None, description="行为规范")
    温度参数: Optional[float] = Field(None, description="生成温度参数", ge=0.0, le=2.0)
    最大令牌数: Optional[int] = Field(None, description="最大令牌数", ge=1, le=32000)
    记忆窗口大小: Optional[int] = Field(None, description="记忆窗口大小", ge=1, le=50)
    启用rag: Optional[bool] = Field(None, description="是否启用rag")
    知识库列表: Optional[List[int]] = Field(None, description="关联知识库列表")
    输出格式: Optional[str] = Field(None, description="输出格式")
    是否公开: Optional[Union[bool, int]] = Field(
        None, description="是否为公开智能体（支持 true/false 或 1/0）"
    )
    是否启用: Optional[bool] = Field(None, description="是否启用智能体")
    标签: Optional[List[str]] = Field(None, description="智能体标签")
    状态: Optional[str] = Field(None, description="智能体状态")
    # 新的三字段数据结构
    自定义回复格式: Optional[Dict[str, Any]] = Field(
        None, description="自定义回复格式配置"
    )
    rag_配置: Optional[Dict[str, Any]] = Field(None, description="RAG检索配置")
    自定义变量: Optional[List[Dict[str, Any]]] = Field(
        None, description="自定义变量列表"
    )
    # 兼容字段 - 用于前端传递RAG相关参数
    检索策略: Optional[str] = Field(None, description="检索策略")
    嵌入模型id: Optional[int] = Field(None, description="嵌入模型id")
    相似度阈值: Optional[float] = Field(None, description="相似度阈值")
    最大检索数量: Optional[int] = Field(None, description="最大检索数量")
    # 查询优化配置字段
    查询优化配置: Optional[Dict[str, Any]] = Field(None, description="查询优化配置")
    # JSON结构化输出相关字段
    json_schema: Optional[Dict[str, Any]] = Field(
        None, description="JSON结构化输出配置"
    )
    pydantic_输出模式: Optional[str] = Field(None, description="Pydantic输出模式")


class 智能体对话测试请求模型(BaseModel):
    """智能体对话测试请求模型"""

    智能体id: int = Field(..., description="智能体id")
    用户消息: str = Field(
        ..., description="用户消息内容", min_length=1, max_length=2000
    )
    会话id: Optional[str] = Field(None, description="会话ID，用于保持对话上下文")
    测试模式: bool = Field(True, description="是否为测试模式")
    自定义变量: Optional[Dict[str, Any]] = Field(
        None, description="自定义变量，用于动态替换提示词中的变量"
    )


class 智能体对话历史查询请求模型(BaseModel):
    """智能体对话历史查询请求模型"""

    智能体id: int = Field(..., description="智能体id")
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    会话id: Optional[str] = Field(None, description="会话ID过滤")
    用户id: Optional[int] = Field(None, description="用户id过滤")
    开始时间: Optional[str] = Field(None, description="开始时间过滤")
    结束时间: Optional[str] = Field(None, description="结束时间过滤")


class 用户智能体分配请求模型(BaseModel):
    """用户智能体分配请求模型"""

    智能体id: int = Field(..., description="智能体id")
    用户id列表: List[int] = Field(..., description="要分配的用户id列表")
    权限级别: str = Field(
        "读写权限",
        description="权限级别：只读权限、读写权限、管理权限",
        pattern="^(只读权限|读写权限|管理权限)$",
    )
    分配类型: str = Field(
        "个人分配",
        description="分配类型：个人分配、共享分配",
        pattern="^(个人分配|共享分配)$",
    )
    备注: Optional[str] = Field(None, description="分配备注", max_length=200)


class 用户智能体取消分配请求模型(BaseModel):
    """用户智能体取消分配请求模型"""

    智能体id: int = Field(..., description="智能体id")
    用户id列表: List[int] = Field(..., description="要取消分配的用户id列表")


class 搜索用户请求模型(BaseModel):
    """搜索用户请求模型"""

    关键词: str = Field(..., description="搜索关键词", min_length=1, max_length=100)
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    备注: Optional[str] = Field(None, description="取消分配备注", max_length=200)


class 智能体删除请求模型(BaseModel):
    """智能体删除请求模型"""

    智能体id: int = Field(..., description="智能体id")


class 智能体详情请求模型(BaseModel):
    """智能体详情请求模型"""

    智能体id: int = Field(..., description="智能体id")


class 智能体知识库请求模型(BaseModel):
    """智能体知识库请求模型"""

    智能体id: int = Field(..., description="智能体id")


class 智能体重新加载请求模型(BaseModel):
    """智能体重新加载请求模型"""

    智能体id: int = Field(..., description="智能体id")


class 智能体分配用户查询请求模型(BaseModel):
    """智能体分配用户查询请求模型"""

    智能体id: int = Field(..., description="智能体id")


class 智能体检索测试请求模型(BaseModel):
    """智能体检索测试请求模型"""

    智能体id: int = Field(..., description="智能体id")
    查询文本: str = Field(
        ..., description="检索查询文本", min_length=1, max_length=1000
    )
    嵌入模型id: Optional[int] = Field(None, description="指定使用的嵌入模型id")
    检索参数: Optional[Dict[str, Any]] = Field(None, description="检索参数")
    手动优化查询: Optional[str] = Field(
        None,
        description="用户手动输入的优化查询语句，用于直接测试第二阶段检索",
        max_length=1000,
    )
    跳过第一阶段: bool = Field(
        False, description="是否跳过第一阶段检索，直接使用手动优化查询进行检索"
    )

    @field_validator("跳过第一阶段")
    @classmethod
    def 验证跳过第一阶段(cls, v, values):
        if v and not values.get("手动优化查询"):
            raise ValueError("跳过第一阶段需要提供手动优化查询")
        return v


class 智能体关联知识库查询请求模型(BaseModel):
    """智能体关联知识库查询请求模型"""

    智能体id: int = Field(..., description="智能体id")


# ==================== 模型供应商请求模型 ====================


class 模型配置列表查询请求模型(BaseModel):
    """模型配置列表查询请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    供应商名称: Optional[str] = Field(None, description="供应商名称过滤")
    模型类型: Optional[str] = Field(None, description="模型类型过滤")
    状态: Optional[str] = Field(None, description="状态过滤")
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")


class 创建模型配置请求模型(BaseModel):
    """创建模型配置请求模型"""

    供应商名称: str = Field(..., description="供应商名称", min_length=1, max_length=50)
    模型名称: str = Field(..., description="模型名称", min_length=1, max_length=100)
    模型类型: str = Field(..., description="模型类型")
    模型功能类型: str = Field(
        "chat",
        description="模型功能类型：chat(对话), embedding(向量)",
        pattern="^(chat|embedding)$",
    )
    API密钥: str = Field(..., description="API密钥", min_length=1, max_length=500)
    API基础URL: Optional[str] = Field(None, description="API基础URL", max_length=200)
    模型参数: Optional[dict] = Field({}, description="模型参数配置")
    是否启用: bool = Field(True, description="是否启用此配置")
    备注: Optional[str] = Field(None, description="配置备注", max_length=500)


class 更新模型配置请求模型(BaseModel):
    """更新模型配置请求模型"""

    供应商名称: Optional[str] = Field(None, description="供应商名称", max_length=50)
    模型名称: Optional[str] = Field(None, description="模型名称", max_length=100)
    模型类型: Optional[str] = Field(None, description="模型类型")
    模型功能类型: Optional[str] = Field(
        None,
        description="模型功能类型：chat(对话), embedding(向量)",
        pattern="^(chat|embedding)$",
    )
    API密钥: Optional[str] = Field(None, description="API密钥", max_length=500)
    API基础URL: Optional[str] = Field(None, description="API基础URL", max_length=200)
    模型参数: Optional[dict] = Field(None, description="模型参数配置")
    是否启用: Optional[bool] = Field(None, description="是否启用此配置")
    备注: Optional[str] = Field(None, description="配置备注", max_length=500)


class 设置默认模型请求模型(BaseModel):
    """设置默认模型请求模型"""

    模型名称: str = Field(
        ..., description="要设置为默认的模型名称", min_length=1, max_length=100
    )


class LangChain知识库列表请求模型(BaseModel):
    """LangChain知识库列表请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    知识库类型: Optional[str] = Field(None, description="知识库类型")
    是否公开: Optional[bool] = Field(None, description="是否公开")


class 知识库文档列表请求模型(BaseModel):
    """知识库文档列表请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)
    搜索关键字: Optional[str] = Field(None, description="搜索关键字")
    文件类型: Optional[str] = Field(None, description="文件类型筛选")
    处理状态: Optional[str] = Field(None, description="处理状态筛选")


class LangChain知识库更新请求模型(BaseModel):
    """LangChain知识库更新请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)
    知识库名称: Optional[str] = Field(
        None, description="知识库名称", min_length=1, max_length=100
    )
    知识库描述: Optional[str] = Field(None, description="知识库描述", max_length=500)
    状态: Optional[str] = Field(
        None, description="状态", pattern="^(active|inactive|processing|error)$"
    )
    向量维度: Optional[int] = Field(None, description="向量维度", ge=1, le=4096)
    嵌入模型: Optional[int] = Field(None, description="嵌入模型id")
    配置信息: Optional[Dict[str, Any]] = Field(None, description="配置信息")


class LangChain知识库克隆请求模型(BaseModel):
    """LangChain知识库克隆请求模型"""

    新知识库名称: str = Field(
        ..., description="新知识库名称", min_length=1, max_length=100
    )


class 文档搜索请求模型(BaseModel):
    """文档搜索请求模型"""

    搜索关键词: str = Field(..., description="搜索关键词", min_length=1, max_length=100)
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)


class 文档更新请求模型(BaseModel):
    """文档更新请求模型"""

    文档id: Union[int, str] = Field(..., description="文档id（支持数字ID或UUID字符串）")
    文档名称: Optional[str] = Field(
        None, description="文档名称", min_length=1, max_length=200
    )
    文档内容: Optional[str] = Field(None, description="文档内容")
    状态: Optional[str] = Field(None, description="状态")
    元数据: Optional[Dict[str, Any]] = Field(None, description="元数据")


class 批量删除文档请求模型(BaseModel):
    """批量删除文档请求模型"""

    文档id列表: List[Union[int, str]] = Field(
        ...,
        description="文档id列表（支持数字ID或UUID字符串）",
        min_length=1,
        max_length=100,
    )


class 检索配置请求模型(BaseModel):
    """检索配置请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)
    检索策略: Optional[str] = Field(
        None, description="检索策略", pattern="^(vector|keyword|hybrid)$"
    )
    相似度阈值: Optional[float] = Field(None, description="相似度阈值", ge=0.0, le=1.0)
    最大检索数量: Optional[int] = Field(None, description="最大检索数量", ge=1, le=100)
    分块大小: Optional[int] = Field(None, description="分块大小", ge=100, le=5000)
    分块重叠: Optional[int] = Field(None, description="分块重叠", ge=0, le=1000)
    分块策略: Optional[str] = Field(None, description="分块策略")


class 检索测试请求模型(BaseModel):
    """检索测试请求模型"""

    查询文本: str = Field(..., description="查询文本", min_length=1, max_length=500)
    嵌入模型id: Optional[int] = Field(None, description="指定使用的嵌入模型id")
    检索参数: Optional[Dict[str, Any]] = Field(None, description="检索参数")


class 知识库检索测试请求模型(BaseModel):
    """知识库检索测试请求模型 - 新版本，支持多知识库和完整参数配置"""

    知识库列表: List[int] = Field(..., description="要测试的知识id列表", min_length=1)
    测试查询: str = Field(
        ..., description="测试查询文本", min_length=1, max_length=1000
    )
    检索配置: Dict[str, Any] = Field(..., description="检索配置参数")
    查询优化配置: Optional[Dict[str, Any]] = Field(None, description="查询优化配置参数")
    测试模式: str = Field("standard", description="测试模式：standard/debug")

    @field_validator("检索配置")
    @classmethod
    def 验证检索配置(cls, v):
        """验证检索配置参数"""
        required_fields = ["检索策略", "嵌入模型", "相似度阈值", "最大检索数量"]
        for field in required_fields:
            if field not in v:
                raise ValueError(f"检索配置缺少必需字段: {field}")
        return v


class 用户权限验证请求模型(BaseModel):
    """用户权限验证请求模型"""

    用户id: int = Field(..., description="用户id", gt=0)
    智能体id: int = Field(..., description="智能体id", gt=0)


class 使用统计查询请求模型(BaseModel):
    """使用统计查询请求模型"""

    开始日期: Optional[str] = Field(None, description="开始日期 YYYY-MM-DD")
    结束日期: Optional[str] = Field(None, description="结束日期 YYYY-MM-DD")
    智能体id: Optional[int] = Field(None, description="指定智能体id")
    用户id: Optional[int] = Field(None, description="指定用户id")
    统计维度: str = Field("日", description="统计维度：日/周/月")
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)


# ==================== API接口实现 ====================


@管理LangChain智能体路由.post("/agents/create", summary="管理员创建智能体")
async def 管理员创建智能体(
    请求数据: 管理员创建智能体请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    管理员创建智能体

    - **智能体名称**: 智能体的显示名称
    - **智能体类型**: 智能体类型
    - **是否公开**: 是否为所有用户可用的公开智能体
    """
    try:
        # 服务层已在构造时初始化，无需额外初始化

        # 准备智能体数据（包含提示词信息）
        智能体数据 = {
            "用户表id": 0,  # 管理员创建的智能体用户id为0
            "智能体名称": 请求数据.智能体名称,
            "智能体描述": 请求数据.智能体描述,
            "模型名称": 请求数据.模型名称,
            "langchain_模型配置表id": getattr(请求数据, "langchain_模型配置表id", None),
            "系统提示词": 请求数据.系统提示词 or "",
            "用户提示词": 请求数据.用户提示词 or "",
            "角色设定": 请求数据.角色设定 or "",
            "行为规范": 请求数据.行为规范 or "",
            "温度参数": 请求数据.温度参数,
            "最大令牌数": 请求数据.最大令牌数,
            "记忆窗口大小": 请求数据.记忆窗口大小,
            "启用rag": 请求数据.启用rag,
            "输出格式": 请求数据.输出格式,
            # 新的三字段数据结构
            "自定义回复格式": 请求数据.自定义回复格式,
            "rag_配置": 请求数据.rag_配置,
            "自定义变量": 请求数据.自定义变量 or [],
            # 兼容处理 - 从前端传递的RAG参数构建rag_配置
            "检索策略": 请求数据.检索策略,
            "嵌入模型id": 请求数据.嵌入模型id,
            "相似度阈值": 请求数据.相似度阈值,
            "最大检索数量": 请求数据.最大检索数量,
            # Pydantic输出模式
            "pydantic_输出模式": 请求数据.pydantic_输出模式,
            # 其他配置
            "是否公开": 请求数据.是否公开,
            "标签": 请求数据.标签 or [],
            "状态": "running",
        }

        # 调用服务层创建智能体
        创建结果 = await LangChain智能体服务实例.创建智能体(智能体数据)

        if 创建结果.get("success"):
            路由日志器.info(
                f"管理员创建智能体成功 - 管理员: {管理员['id']}, 智能体id: {创建结果.get('智能体id')}"
            )

            return 统一响应模型.成功(
                {
                    "智能体id": 创建结果.get("智能体id"),
                    "智能体名称": 请求数据.智能体名称,
                    "是否公开": 请求数据.是否公开,
                    "创建时间": 创建结果.get("创建时间"),
                },
                "智能体创建成功",
            )
        else:
            return 统一响应模型.失败(状态.LangChain.智能体创建失败, 创建结果.get("error", "创建智能体失败"))

    except Exception as e:
        错误日志器.error(
            f"管理员创建智能体API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.智能体创建失败, f"创建智能体失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/list", summary="获取所有智能体列表")
async def 获取所有智能体列表(
    请求数据: 智能体列表查询请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取所有智能体列表（管理员视图）
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求获取智能体列表，请求参数: {请求数据.model_dump()}"
        )

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "状态": 请求数据.状态,
            "搜索关键词": 请求数据.搜索关键词,
            "是否公开": 请求数据.是否公开,
        }

        # 调用服务层获取数据
        结果 = await LangChain智能体服务实例.获取智能体列表详细(查询参数)

        路由日志器.info(
            f"管理员 {管理员['id']} 获取智能体列表成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取智能体列表成功")

    except Exception as e:
        错误日志器.error(
            f"获取智能体列表API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.智能体更新失败, f"获取智能体列表失败: {str(e)}")


# 旧的分配函数已删除，使用新的按智能体id分配的函数


@管理LangChain智能体路由.post("/assignments/list", summary="获取用户智能体分配列表")
async def 获取用户智能体分配列表(
    请求数据: 用户分配查询请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取用户智能体分配列表
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取用户智能体分配列表")

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "用户id": 请求数据.用户id,
            "智能体id": 请求数据.智能体id,
            "状态": 请求数据.状态,
        }

        # 调用服务层获取数据
        结果 = await LangChain智能体服务实例.获取用户智能体分配列表详细(查询参数)

        路由日志器.info(
            f"管理员 {管理员['id']} 获取用户智能体分配列表成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取分配列表成功")

    except Exception as e:
        错误日志器.error(
            f"获取分配列表API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取分配列表失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/assignments/{assignment_id}/revoke", summary="撤销用户智能体分配"
)
async def 撤销用户智能体分配(
    assignment_id: int = Path(..., description="分配ID"),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    撤销用户智能体分配
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求撤销智能体分配: {assignment_id}")

        # 调用服务层撤销单个分配
        结果 = await LangChain智能体服务实例.撤销单个智能体分配(assignment_id)

        if 结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 撤销智能体分配成功: {assignment_id}"
            )
            return 统一响应模型.成功(结果, "撤销分配成功")
        else:
            return 统一响应模型.失败(状态.LangChain.删除操作失败, 结果.get("error", "撤销分配失败"))

    except Exception as e:
        错误日志器.error(
            f"撤销智能体分配API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.删除操作失败, f"撤销智能体分配失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/delete", summary="删除智能体")
async def 删除智能体(
    请求数据: 智能体删除请求模型 = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    删除智能体（管理员操作）
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求删除智能体: {请求数据.智能体id}")

        # 调用服务层删除智能体
        结果 = await LangChain智能体服务实例.删除智能体(请求数据.智能体id)

        if 结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 删除智能体成功: {请求数据.智能体id}"
            )
            return 统一响应模型.成功(结果, "智能体删除成功")
        else:
            return 统一响应模型.失败(状态.LangChain.删除操作失败, 结果.get("error", "删除智能体失败"))

    except Exception as e:
        错误日志器.error(
            f"删除智能体API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.删除操作失败, f"删除智能体失败: {str(e)}")


@管理LangChain智能体路由.post("/statistics", summary="获取智能体统计信息")
async def 获取智能体统计信息(管理员: dict = Depends(获取当前管理员用户)):
    """
    获取智能体统计信息
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取智能体统计信息")

        # 调用服务层获取统计信息
        统计信息 = await LangChain智能体服务实例.获取智能体统计信息()

        路由日志器.info(f"管理员 {管理员['id']} 获取智能体统计信息成功")

        return 统一响应模型.成功(统计信息, "获取统计信息成功")

    except Exception as e:
        错误日志器.error(
            f"获取统计信息API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取统计信息失败: {str(e)}")


@管理LangChain智能体路由.post("/users/search", summary="搜索用户")
async def 搜索用户(
    请求数据: 搜索用户请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    搜索用户（用于智能体分配）
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求搜索用户: {请求数据.关键词}")

        # 调用服务层搜索用户
        结果 = await LangChain智能体服务实例.搜索用户(
            搜索关键词=请求数据.关键词, 页码=请求数据.页码, 每页数量=请求数据.每页数量
        )

        路由日志器.info(
            f"管理员 {管理员['id']} 搜索用户成功，关键词: {请求数据.关键词}，总数: {结果['总数量']}"
        )
        return 统一响应模型.成功(结果, "搜索用户成功")

    except Exception as e:
        错误日志器.error(
            f"搜索用户API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.配置错误, f"搜索用户失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/update", summary="更新智能体配置")
async def 更新智能体配置(
    请求数据: 更新智能体请求模型 = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    更新智能体配置 - 优雅处理保存流程

    支持的更新字段：
    - 基本信息：智能体名称、智能体描述等
    - 模型配置：模型名称、温度参数、最大令牌数等
    - 高级配置：JSON Schema结构化输出、RAG配置等
    - 提示词配置：系统提示词、用户提示词、角色设定、行为规范
    """
    try:
        路由日志器.info(
            f"👤 管理员 {管理员['id']} 请求更新智能体配置: agent_id={请求数据.智能体id}"
        )

        # 构建更新数据 - 只传递非空字段
        更新数据 = {}

        # 基本配置字段映射
        字段映射 = {
            "智能体名称": "智能体名称",
            "智能体描述": "智能体描述",
            "模型名称": "模型名称",
            "langchain_模型配置表id": "langchain_模型配置表id",
            "温度参数": "温度参数",
            "最大令牌数": "最大令牌数",
            "记忆窗口大小": "记忆窗口大小",
            "启用rag": "启用rag",
            "输出格式": "输出格式",
            "状态": "状态",
            "是否公开": "是否公开",
            "标签": "标签",
        }

        # 处理基本字段
        for 前端字段, 后端字段 in 字段映射.items():
            if hasattr(请求数据, 前端字段):
                值 = getattr(请求数据, 前端字段)
                if 值 is not None:
                    更新数据[后端字段] = 值

        # 处理提示词配置字段
        提示词字段 = ["系统提示词", "用户提示词", "角色设定", "行为规范"]
        for 字段 in 提示词字段:
            if hasattr(请求数据, 字段):
                值 = getattr(请求数据, 字段)
                if 值 is not None:
                    更新数据[字段] = 值

        # 处理自定义回复格式 - 主要字段
        if hasattr(请求数据, "自定义回复格式") and 请求数据.自定义回复格式 is not None:
            更新数据["自定义回复格式"] = 请求数据.自定义回复格式
            路由日志器.info(
                f"🔧 检测到自定义回复格式配置: {请求数据.自定义回复格式.get('输出模式', 'unknown')}"
            )

        # 处理JSON Schema字段 - 转换为自定义回复格式
        if hasattr(请求数据, "json_schema") and 请求数据.json_schema is not None:
            更新数据["自定义回复格式"] = 请求数据.json_schema
            路由日志器.info(
                f"🔧 检测到JSON Schema配置，转换为自定义回复格式: {len(请求数据.json_schema.get('properties', {}))}个字段"
            )

        # 处理RAG配置兼容字段
        rag兼容字段 = ["检索策略", "嵌入模型id", "相似度阈值", "最大检索数量"]
        for 字段 in rag兼容字段:
            if hasattr(请求数据, 字段):
                值 = getattr(请求数据, 字段)
                if 值 is not None:
                    更新数据[字段] = 值

        # 处理自定义变量
        if hasattr(请求数据, "自定义变量") and 请求数据.自定义变量 is not None:
            更新数据["自定义变量"] = 请求数据.自定义变量

        # 处理知识库列表
        if hasattr(请求数据, "知识库列表") and 请求数据.知识库列表 is not None:
            更新数据["知识库列表"] = 请求数据.知识库列表

        # 处理查询优化配置
        if hasattr(请求数据, "查询优化配置") and 请求数据.查询优化配置 is not None:
            路由日志器.info(f"🔧 接收到查询优化配置: {请求数据.查询优化配置}")
            更新数据["查询优化配置"] = 请求数据.查询优化配置

        路由日志器.info(f"📤 准备更新字段: {list(更新数据.keys())}")

        # 调用服务层更新智能体配置
        结果 = await LangChain智能体服务实例.更新智能体(请求数据.智能体id, 更新数据)

        # 统一响应处理
        if 结果.get("status") == 100:  # 智能体更新成功
            路由日志器.info(
                f"✅ 管理员 {管理员['id']} 更新智能体配置成功: agent_id={请求数据.智能体id}"
            )

            # 构建优化的响应数据
            响应数据 = {
                "智能体id": 请求数据.智能体id,
                "智能体详情": 结果["data"]["智能体详情"],
                "更新结果": {
                    "更新字段数量": len(结果["data"]["更新字段"]),
                    "更新字段列表": 结果["data"]["更新字段"],
                    "更新时间": 结果["data"]["更新时间"],
                    "重载状态": 结果["data"]["重载状态"],
                },
                "操作信息": {
                    "操作者ID": 管理员["id"],
                    "操作时间": datetime.now().isoformat(),
                    "操作类型": "更新智能体配置",
                },
            }

            return 统一响应模型.成功(响应数据, "智能体配置更新成功")

        else:
            # 处理各种错误情况
            路由日志器.error(f"❌ 智能体配置更新失败: {结果.get('message')}")

            # 直接使用服务层返回的状态码
            状态码 = 结果.get("status", 1502)  # 默认为智能体更新失败
            return 统一响应模型.失败(状态码, 结果.get("message", "更新智能体配置失败"))

    except ValueError as e:
        路由日志器.error(f"❌ 请求数据验证错误 (管理员: {管理员['id']}): {str(e)}")
        return 统一响应模型.失败(1400, f"请求数据验证错误: {str(e)}")

    except Exception as e:
        错误日志器.error(
            f"❌ 更新智能体API异常 (管理员: {管理员['id']}, agent_id: {请求数据.智能体id}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态.LangChain.智能体更新失败, f"更新智能体失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/detail", summary="获取智能体详情")
async def 获取智能体详情(
    请求数据: 智能体详情请求模型 = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取智能体详情（管理员视图）
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求获取智能体详情: {请求数据.智能体id}"
        )

        # 调用服务层获取智能体详情
        智能体详情 = await LangChain智能体服务实例.获取智能体详情(请求数据.智能体id)

        if 智能体详情:
            路由日志器.info(
                f"管理员 {管理员['id']} 获取智能体详情成功: {请求数据.智能体id}"
            )
            return 统一响应模型.成功(智能体详情, "获取智能体详情成功")
        else:
            return 统一响应模型.失败(1404, "智能体不存在")

    except Exception as e:
        错误日志器.error(
            f"获取智能体详情API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取智能体详情失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/tools", summary="获取智能体工具关联")
async def 获取智能体工具关联(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取智能体工具关联配置
    """
    try:
        智能体id = 请求数据.get("智能体id") or 请求数据.get("智能体id")
        if not 智能体id:
            return 统一响应模型.失败(1400, "智能体id参数缺失")

        路由日志器.info(f"管理员 {管理员['id']} 请求获取智能体工具关联: {智能体id}")

        # 初始化工具数据层
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 使用工具数据层获取完整的工具关联信息
        详细工具关联 = await LangChain工具数据层实例.获取智能体工具关联(智能体id)

        # 转换为前端需要的格式
        工具列表 = []
        for 关联 in 详细工具关联:
            工具列表.append(
                {
                    "工具名称": 关联.get("工具名称"),
                    "工具描述": 关联.get("工具描述", ""),
                    "启用状态": 关联.get("启用状态", False),
                    "工具配置": 关联.get("工具配置", {}),
                    "创建时间": 关联.get("创建时间"),
                }
            )

        # 返回优化的数据结构
        结果数据 = {"工具列表": 工具列表, "总数量": len(工具列表), "智能体id": 智能体id}

        路由日志器.info(
            f"管理员 {管理员['id']} 获取智能体工具关联成功: {len(工具列表)} 个工具"
        )
        return 统一响应模型.成功(结果数据, "获取工具关联成功")

    except Exception as e:
        错误日志器.error(f"获取智能体工具关联API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取工具关联失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/tools/save", summary="保存智能体工具关联")
async def 保存智能体工具关联(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    保存智能体工具关联配置
    """
    try:
        智能体id = 请求数据.get("智能体id")
        工具列表 = 请求数据.get("工具列表", [])

        路由日志器.info(
            f"管理员 {管理员['id']} 保存智能体工具关联: 智能体id={智能体id}, 工具数量={len(工具列表)}"
        )

        # 转换为标准格式
        标准化工具列表 = []
        for 工具 in 工具列表:
            标准化工具列表.append(工具.get("工具名称") or 工具.get("name"))

        # 验证智能体id
        if not 智能体id or not isinstance(智能体id, int):
            return 统一响应模型.失败(1400, "智能体id无效")

        # 保存工具关联 - 使用统一工具数据层
        保存结果 = await LangChain工具数据层实例.批量保存智能体工具关联(
            智能体id,
            [
                {"工具名称": 工具名称, "启用状态": True}
                for 工具名称 in 标准化工具列表
                if 工具名称
            ],
        )
        保存成功 = 保存结果.get("success", False)

        if 保存成功:
            # 返回保存结果和统计信息
            结果数据 = {
                "保存成功": True,
                "工具数量": len(标准化工具列表),
                "智能体id": 智能体id,
                "保存时间": datetime.now().isoformat(),
            }
            路由日志器.info(f"管理员 {管理员['id']} 保存智能体工具关联成功")
            return 统一响应模型.成功(结果数据, "工具关联保存成功")
        else:
            return 统一响应模型.失败(状态.LangChain.配置错误, "工具关联保存失败")

    except Exception as e:
        错误日志器.error(f"保存智能体工具关联API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"保存工具关联失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/tools/toggle", summary="切换智能体工具开关")
async def 切换智能体工具开关(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    单独切换智能体工具的启用/禁用状态

    请求体格式：
    {
        "智能体id": 123,      // 智能体id
        "工具id": 123,        // 工具配置表的ID
        "启用状态": true/false,
        "工具配置": {
            "priority": 5,
            "description": "工具描述"
        }
    }
    """
    try:
        智能体id = 请求数据.get("智能体id")
        工具id = 请求数据.get("工具id")
        启用状态 = 请求数据.get("启用状态", True)
        工具配置 = 请求数据.get("工具配置", {})

        # 验证必需参数
        if not 智能体id:
            return 统一响应模型.失败(1400, "智能体id参数缺失")

        if not 工具id:
            return 统一响应模型.失败(1400, "工具id参数缺失")

        # 直接使用工具id获取工具配置
        工具配置信息 = await LangChain工具数据层实例.根据ID获取工具配置(工具id)
        if not 工具配置信息:
            return 统一响应模型.失败(1404, f"工具 {工具id} 不存在")

        工具名称 = 工具配置信息.get("工具名称")
        if not 工具名称:
            return 统一响应模型.失败(1400, f"工具 {工具id} 对应的工具名称为空")

        路由日志器.info(
            f"管理员 {管理员['id']} 切换智能体工具开关: 智能体id={智能体id}, 工具id={工具id}, 工具名称={工具名称}, 启用={启用状态}"
        )

        # 验证智能体是否存在
        智能体存在 = await LangChain智能体数据层实例.验证智能体存在(智能体id)
        if not 智能体存在:
            return 统一响应模型.失败(1404, "智能体不存在")

        # 初始化工具数据层
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 直接使用UPSERT操作，简化逻辑
        更新成功 = await LangChain工具数据层实例.创建或更新智能体工具关联(
            智能体id, 工具id, 工具配置, 启用状态
        )

        if 更新成功:
            操作结果 = "启用" if 启用状态 else "禁用"
        else:
            操作结果 = "操作失败"

        if 更新成功:
            路由日志器.info(f"管理员 {管理员['id']} 工具开关切换成功: {操作结果}")
            return 统一响应模型.成功(
                {
                    "智能体id": 智能体id,
                    "工具id": 工具id,
                    "工具名称": 工具名称,
                    "启用状态": 启用状态,
                    "操作类型": 操作结果,
                    "操作时间": datetime.now().isoformat(),
                },
                f"工具{工具名称}{'启用' if 启用状态 else '禁用'}成功",
            )
        else:
            return 统一响应模型.失败(状态.LangChain.配置错误, f"工具开关切换失败: {操作结果}")

    except Exception as e:
        错误日志器.error(
            f"切换智能体工具开关API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(状态.LangChain.配置错误, f"切换工具开关失败: {str(e)}")


@管理LangChain智能体路由.post("/tools/list", summary="获取可用工具列表")
async def 获取可用工具列表(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取可用工具列表
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取可用工具列表")

        # 初始化内部函数包装器
        if not 内部函数包装器实例.已初始化:
            await 内部函数包装器实例.初始化()

        # 获取内部函数工具列表
        内部工具字典 = await 内部函数包装器实例.获取可用工具列表()
        工具元数据字典 = await 内部函数包装器实例.获取工具元数据()

        # 转换为前端需要的格式
        工具列表 = []
        for 工具名称, 工具实例 in 内部工具字典.items():
            元数据 = 工具元数据字典.get(工具名称, {})

            # 处理工具参数 - 避免序列化Pydantic模型类
            工具参数 = None
            if hasattr(工具实例, "args_schema") and 工具实例.args_schema:
                try:
                    # 如果是Pydantic模型类，获取其schema
                    if hasattr(工具实例.args_schema, "model_json_schema"):
                        工具参数 = 工具实例.args_schema.model_json_schema()
                    else:
                        工具参数 = str(工具实例.args_schema)
                except Exception:
                    工具参数 = None

            # 尝试从数据库获取工具id（通过工具名称查询）
            try:
                工具配置 = await LangChain工具数据层实例.获取工具配置(工具名称)
                工具id = 工具配置.get("id") if 工具配置 else None
            except Exception as e:
                路由日志器.warning(f"获取工具配置失败 ({工具名称}): {str(e)}")
                工具id = None

            工具列表.append(
                {
                    "工具id": 工具id,  # 添加工具id
                    "id": 工具id,  # 兼容不同的ID字段名
                    "工具名称": 工具名称,
                    "工具描述": getattr(工具实例, "description", "")
                    or 元数据.get("描述", ""),
                    "工具分类": 元数据.get("分类", "内部工具"),
                    "启用状态": True,
                    "权限要求": 元数据.get("权限要求", ""),
                    "工具参数": 工具参数,
                    "创建时间": datetime.now().isoformat(),
                }
            )

        # 同时获取LangChain工具管理器中的工具
        if LangChain工具管理器实例.已初始化:
            管理器工具配置 = LangChain工具管理器实例.工具配置
            for 工具名称, 配置 in 管理器工具配置.items():
                if 工具名称 not in 内部工具字典:  # 避免重复
                    # 尝试从数据库获取工具id（通过工具名称查询）
                    try:
                        工具配置 = await LangChain工具数据层实例.获取工具配置(工具名称)
                        工具id = 工具配置.get("id") if 工具配置 else None
                    except Exception as e:
                        路由日志器.warning(f"获取工具配置失败 ({工具名称}): {str(e)}")
                        工具id = None

                    工具列表.append(
                        {
                            "工具id": 工具id,  # 添加工具id
                            "id": 工具id,  # 兼容不同的ID字段名
                            "工具名称": 工具名称,
                            "工具描述": 配置.get("描述", ""),
                            "启用状态": 配置.get("启用状态", True),
                            "权限要求": 配置.get("权限要求", ""),
                            "工具参数": None,
                            "创建时间": 配置.get(
                                "注册时间", datetime.now()
                            ).isoformat(),
                        }
                    )

        路由日志器.info(
            f"管理员 {管理员['id']} 获取可用工具列表成功: {len(工具列表)} 个工具"
        )
        return 统一响应模型.成功(工具列表, "获取可用工具列表成功")

    except Exception as e:
        错误日志器.error(f"获取可用工具列表API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取可用工具列表失败: {str(e)}")


@管理LangChain智能体路由.post("/tools/test", summary="测试工具调用")
async def 测试工具调用(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    测试工具调用功能 - 使用增强的测试功能
    """
    try:
        工具名称 = 请求数据.get("工具名称")

        if not 工具名称:
            return 统一响应模型.失败(1400, "工具名称参数缺失")

        路由日志器.info(f"管理员 {管理员['id']} 测试工具: {工具名称}")

        # 使用增强的测试工具方法
        测试结果 = await LangChain工具管理器实例.测试工具(工具名称, 用户id=管理员["id"])

        # 根据新的状态码体系处理结果
        if 测试结果.get("success"):
            路由日志器.info(f"管理员 {管理员['id']} 工具测试成功: {工具名称}")
            return 统一响应模型.成功(
                测试结果.get("测试结果"), 测试结果.get("message", "工具测试成功")
            )
        else:
            # 使用工具调用状态码
            状态码 = 测试结果.get("status_code", 1515)
            return 统一响应模型.失败(状态码, 测试结果.get("message", "工具测试失败"))

    except Exception as e:
        错误日志器.error(f"测试工具调用API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"工具测试失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/knowledge-bases", summary="获取智能体关联知识库")
async def 获取智能体关联知识库(
    请求数据: 智能体知识库请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    获取智能体关联的所有知识库详细信息

    请求体格式：
    {
        "智能体id": 123
    }

    返回数据包含知识库的完整信息（ID、名称、描述、状态等）
    """
    try:
        智能体id = 请求数据.智能体id

        路由日志器.info(
            f"👤 管理员 {管理员['id']} 请求获取智能体关联知识库: agent_id={智能体id}"
        )

        # 获取智能体关联的知识库详细信息
        知识库列表 = await LangChain智能体服务实例.获取智能体关联知识库详情(智能体id)

        路由日志器.info(
            f"✅ 管理员 {管理员['id']} 获取智能体关联知识库成功: agent_id={智能体id}, 知识库数量={len(知识库列表)}"
        )

        return 统一响应模型.成功(知识库列表, "获取智能体关联知识库成功")

    except Exception as e:
        错误日志器.error(f"获取智能体关联知识库API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取智能体关联知识库失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/tools-detail", summary="获取智能体关联工具详情")
async def 获取智能体关联工具详情(
    请求数据: 智能体知识库请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    获取智能体关联的所有工具详细信息

    请求体格式：
    {
        "智能体id": 123
    }

    返回数据包含工具的完整信息（名称、描述、状态、配置等）
    """
    try:
        智能体id = 请求数据.智能体id

        路由日志器.info(
            f"👤 管理员 {管理员['id']} 请求获取智能体关联工具详情: agent_id={智能体id}"
        )

        # 初始化工具数据层
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 获取智能体关联的工具详细信息
        工具关联列表 = await LangChain工具数据层实例.获取智能体工具关联(智能体id)

        # 构建优化的数据结构
        结果数据 = {
            "工具列表": 工具关联列表,
            "总数量": len(工具关联列表),
            "智能体id": 智能体id,
        }

        路由日志器.info(
            f"✅ 管理员 {管理员['id']} 获取智能体关联工具详情成功: agent_id={智能体id}, 工具数量={len(工具关联列表)}"
        )

        return 统一响应模型.成功(结果数据, "获取智能体关联工具详情成功")

    except Exception as e:
        错误日志器.error(f"获取智能体关联工具详情API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"获取智能体关联工具详情失败: {str(e)}")


# 性能统计接口已移除 - 相关功能已清理














@管理LangChain智能体路由.post("/tools/test-connection", summary="测试工具连接")
async def 测试工具连接(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    测试内部函数工具系统连接
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 测试工具连接")

        # 初始化内部函数包装器
        if not 内部函数包装器实例.已初始化:
            await 内部函数包装器实例.初始化()

        # 测试连接 - 检查是否能获取工具列表
        内部工具字典 = await 内部函数包装器实例.获取可用工具列表()
        工具数量 = len(内部工具字典)

        if 工具数量 > 0:
            路由日志器.info(
                f"管理员 {管理员['id']} 工具连接测试成功，发现 {工具数量} 个工具"
            )
            return 统一响应模型.成功(
                {
                    "工具数量": 工具数量,
                    "工具列表": list(内部工具字典.keys()),
                    "测试时间": datetime.now().isoformat(),
                },
                "工具连接测试成功",
            )
        else:
            return 统一响应模型.失败(状态.LangChain.配置错误, "未发现可用的内部函数工具")

    except Exception as e:
        错误日志器.error(f"测试工具连接API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.配置错误, f"工具连接测试失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/tools/verify", summary="验证智能体工具调用")
async def 验证智能体工具调用(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    验证智能体工具调用功能

    请求体格式：
    {
        "智能体id": 123,
        "工具名称": "工具名称",
        "参数": {}
    }
    """
    try:
        # 提取并验证必需参数
        智能体id = 请求数据.get("智能体id")
        工具名称 = 请求数据.get("工具名称")

        if not 智能体id or not 工具名称:
            缺失参数 = []
            if not 智能体id:
                缺失参数.append("智能体id")
            if not 工具名称:
                缺失参数.append("工具名称")
            return 统一响应模型.失败(1400, f"参数缺失: {', '.join(缺失参数)}")

        # 类型转换：确保智能体id是整数类型
        try:
            智能体id = int(智能体id)
        except (ValueError, TypeError):
            return 统一响应模型.失败(1400, f"智能体id必须是有效的整数: {智能体id}")

        参数 = 请求数据.get("参数", {})

        路由日志器.info(
            f"管理员 {管理员['id']} 验证智能体工具调用: 智能体id={智能体id}, 工具名称={工具名称}"
        )

        # 智能体服务已使用简化初始化模式

        # 调用智能体服务的工具验证方法
        验证结果 = await LangChain智能体服务实例.验证工具调用(智能体id, 工具名称, 参数)

        if 验证结果.get("success"):
            路由日志器.info(f"管理员 {管理员['id']} 智能体工具验证成功: {工具名称}")
            return 统一响应模型.成功(验证结果.get("data"), "智能体工具验证成功")
        else:
            错误信息 = 验证结果.get("error", "智能体工具验证失败")
            路由日志器.warning(f"管理员 {管理员['id']} 智能体工具验证失败: {错误信息}")
            return 统一响应模型.失败(1519, 错误信息)

    except Exception as e:
        错误日志器.error(f"验证智能体工具调用API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1519, f"智能体工具验证失败: {str(e)}")


@管理LangChain智能体路由.post("/tools/toggle", summary="切换工具启用状态")
async def 切换工具启用状态(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    切换单个工具的全局启用状态
    注意：内部函数工具的全局启用状态目前通过工具配置表管理
    """
    try:
        工具名称 = 请求数据.get("工具名称")
        启用状态 = 请求数据.get("启用状态", True)

        if not 工具名称:
            return 统一响应模型.失败(1400, "工具名称参数缺失")

        路由日志器.info(
            f"管理员 {管理员['id']} 切换工具启用状态: {工具名称} -> {启用状态}"
        )

        # 初始化工具数据层
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 获取现有工具配置
        现有配置 = await LangChain工具数据层实例.获取工具配置(工具名称)
        if not 现有配置:
            return 统一响应模型.失败(1404, f"工具不存在: {工具名称}")

        # 构建完整的更新配置
        更新配置 = {
            "工具描述": 现有配置.get("工具描述", ""),
            "工具参数": 现有配置.get("工具参数", ""),
            "权限要求": 现有配置.get("权限要求", ""),
            "安全级别": 现有配置.get("安全级别", 1),
            "启用状态": 启用状态,  # 只更新启用状态
            "超时时间": 现有配置.get("超时时间", 30),
            "重试次数": 现有配置.get("重试次数", 3),
        }

        # 更新工具配置表中的启用状态
        更新结果 = await LangChain工具数据层实例.更新工具配置(工具名称, 更新配置)

        if 更新结果:
            路由日志器.info(f"管理员 {管理员['id']} 工具状态切换成功: {工具名称}")
            return 统一响应模型.成功(
                {
                    "工具名称": 工具名称,
                    "启用状态": 启用状态,
                    "操作时间": datetime.now().isoformat(),
                },
                f"工具{工具名称}{'启用' if 启用状态 else '禁用'}成功",
            )
        else:
            return 统一响应模型.失败(1518, "工具状态切换失败")

    except Exception as e:
        错误日志器.error(f"切换工具启用状态API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1518, f"切换工具启用状态失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/test", summary="管理员测试智能体对话")
async def 管理员测试智能体对话(
    请求数据: 智能体对话测试请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    管理员专用的智能体对话测试接口

    功能特点:
    - 不消耗用户算力
    - 自动RAG增强（如果启用）
    - 支持所有状态的智能体测试
    - 统一的错误处理
    """

    try:
        # 使用统一工具进行验证和处理
        from 服务.LangChain_RAG测试工具 import RAG测试工具

        # 智能体服务已使用简化初始化模式

        # 验证智能体（使用现有方法）
        验证结果 = await LangChain智能体服务实例.验证智能体存在(请求数据.智能体id)
        if not 验证结果.get("success"):
            return 统一响应模型.失败(1404, 验证结果.get("error", "智能体不存在"))

        # 生成测试会话ID
        测试会话id = 请求数据.会话id or RAG测试工具.生成会话ID("admin_test")

        # 调用RAG增强对话
        对话结果 = await LangChain智能体服务实例.管理员RAG增强对话(
            智能体id=请求数据.智能体id,
            用户表id=管理员["id"],
            用户消息=请求数据.用户消息,
            会话id=测试会话id,
            测试模式=True,
            自定义变量=请求数据.自定义变量,
        )

        # 处理对话结果
        if 对话结果.get("status") == 100:
            对话数据 = 对话结果.get("data", {})

            # 简化的响应数据
            响应数据 = {
                "智能体id": 请求数据.智能体id,
                "会话ID": 测试会话id,
                "原始用户消息": 请求数据.用户消息,
                "智能体回复": 对话数据.get("智能体回复"),
                "RAG增强": 对话数据.get("RAG增强", False),
                "RAG检索信息": 对话数据.get("RAG检索信息"),
                "处理时长": 对话数据.get("处理时长", 0),
                "令牌消耗": 对话数据.get("令牌消耗", 0),
                "错误信息": 对话数据.get("错误信息"),
            }
            return 统一响应模型.成功(响应数据, "智能体对话测试成功")
        else:
            错误信息 = 对话结果.get("message", "未知错误")
            return 统一响应模型.失败(状态.LangChain.服务不可用, f"智能体对话失败: {错误信息}")

    except Exception as e:
        错误日志器.error(f"管理员测试智能体对话异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(状态.LangChain.服务不可用, f"智能体对话测试失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/conversations", summary="获取智能体对话历史")
async def 获取智能体对话历史(
    请求数据: 智能体对话历史查询请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    获取智能体的对话历史记录
    """
    try:
        # 验证智能体是否存在
        验证结果 = await LangChain智能体服务实例.验证智能体存在(请求数据.智能体id)
        if not 验证结果.get("success"):
            return 统一响应模型.失败(1404, 验证结果.get("error", "智能体不存在"))

        智能体信息 = 验证结果["智能体信息"]

        # 构建查询参数
        查询参数 = {"页码": 请求数据.页码, "每页数量": 请求数据.每页数量}

        if 请求数据.会话id:
            查询参数["会话id"] = 请求数据.会话id
        if 请求数据.用户表id:
            查询参数["用户表id"] = 请求数据.用户表id
        if 请求数据.开始时间:
            查询参数["开始时间"] = 请求数据.开始时间
        if 请求数据.结束时间:
            查询参数["结束时间"] = 请求数据.结束时间

        # 调用服务层获取对话历史
        对话历史结果 = await LangChain智能体服务实例.获取对话历史详细(
            请求数据.智能体id, 查询参数
        )

        if 对话历史结果.get("success"):
            return 统一响应模型.成功(
                {
                    "智能体id": 请求数据.智能体id,
                    "智能体名称": 智能体信息["智能体名称"],
                    "对话历史": 对话历史结果["对话历史"],
                    "总数量": 对话历史结果["总数量"],
                    "页码": 对话历史结果["页码"],
                    "每页数量": 对话历史结果["每页数量"],
                    "总页数": 对话历史结果["总页数"],
                },
                "获取对话历史成功",
            )
        else:
            return 统一响应模型.失败(
                1500, f"获取对话历史失败: {对话历史结果.get('error', '未知错误')}"
            )

    except Exception as e:
        错误日志器.error(
            f"获取智能体对话历史API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1513, f"获取对话历史失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/reload", summary="重新加载智能体配置")
async def 重新加载智能体配置(
    请求数据: 智能体重新加载请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    重新加载智能体配置（用于配置更新后立即生效）
    """
    try:
        # 重新加载智能体
        重新加载成功 = await LangChain智能体服务实例.重新加载智能体(
            str(请求数据.智能体id)
        )

        if 重新加载成功:
            路由日志器.info(
                f"智能体重新加载成功 - 管理员: {管理员['id']}, 智能体id: {请求数据.智能体id}"
            )
            return 统一响应模型.成功(
                {
                    "智能体id": 请求数据.智能体id,
                    "重新加载时间": datetime.now().isoformat(),
                },
                "智能体配置重新加载成功",
            )
        else:
            return 统一响应模型.失败(1514, "智能体重新加载失败")

    except Exception as e:
        错误日志器.error(
            f"重新加载智能体配置API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1515, f"重新加载智能体配置失败: {str(e)}")


@管理LangChain智能体路由.post("/system/reload", summary="重新加载所有智能体")
async def 重新加载所有智能体(管理员: dict = Depends(获取当前管理员用户)):
    """
    重新加载所有智能体配置
    """
    try:
        # 重新加载所有智能体
        重新加载成功 = await LangChain智能体服务实例.重新加载智能体()

        if 重新加载成功:
            路由日志器.info(f"所有智能体重新加载成功 - 管理员: {管理员['id']}")
            return 统一响应模型.成功(
                {"重新加载时间": datetime.now().isoformat()},
                "所有智能体配置重新加载成功",
            )
        else:
            return 统一响应模型.失败(1516, "智能体重新加载失败")

    except Exception as e:
        错误日志器.error(
            f"重新加载所有智能体API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1517, f"重新加载所有智能体失败: {str(e)}")


@管理LangChain智能体路由.post("/system/debug", summary="获取智能体工厂调试信息")
async def 获取智能体工厂调试信息(管理员: dict = Depends(获取当前管理员用户)):
    """
    获取智能体工厂调试信息
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取智能体工厂调试信息")

        # 获取智能体服务状态
        调试信息 = {
            "服务状态": "运行中" if LangChain智能体服务实例.已初始化 else "未初始化",
            "智能体实例数量": len(LangChain智能体服务实例.智能体实例池)
            if LangChain智能体服务实例.已初始化
            else 0,
            "活跃用户数": len(LangChain智能体服务实例.用户智能体映射)
            if LangChain智能体服务实例.已初始化
            else 0,
            "系统统计": LangChain智能体服务实例.获取系统统计()
            if LangChain智能体服务实例.已初始化
            else {},
            "初始化时间": LangChain智能体服务实例.初始化时间.isoformat()
            if LangChain智能体服务实例.已初始化
            else None,
            "获取时间": datetime.now().isoformat(),
        }

        路由日志器.info(f"管理员 {管理员['id']} 获取智能体工厂调试信息成功")
        return 统一响应模型.成功(调试信息, "获取智能体工厂调试信息成功")

    except Exception as e:
        错误日志器.error(
            f"获取智能体工厂调试信息API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1518, f"获取智能体工厂调试信息失败: {str(e)}")


@管理LangChain智能体路由.post("/system/config", summary="获取LangChain系统配置")
async def 获取LangChain系统配置(管理员: dict = Depends(获取当前管理员用户)):
    """
    获取LangChain系统配置信息
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取LangChain系统配置")

        # 获取LangChain系统配置
        系统配置 = {
            "系统名称": "LangChain智能体管理系统",
            "系统版本": "1.0.0",
            "默认模型": "qwen-turbo",
            "最大并发智能体数": 100,
            "会话超时时间": 3600,
            "记忆窗口默认大小": 10,
            "RAG功能": {
                "启用状态": True,
                "支持的文档格式": ["txt", "pdf", "docx", "md"],
                "向量维度": 1536,
                "配置说明": "检索参数从智能体关联配置表中获取",
            },
            "模型配置": {
                "温度参数范围": [0.0, 2.0],
                "最大令牌数范围": [1, 32000],
                "默认温度": 0.7,
                "默认最大令牌数": 4000,
            },
            "安全配置": {"内容过滤": True, "敏感词检测": True, "访问控制": True},
            "性能配置": {
                "缓存启用": True,
                "缓存过期时间": 1800,
                "请求超时": 60,
                "重试次数": 3,
            },
            "获取时间": datetime.now().isoformat(),
        }

        路由日志器.info(f"管理员 {管理员['id']} 获取LangChain系统配置成功")
        return 统一响应模型.成功(系统配置, "获取LangChain系统配置成功")

    except Exception as e:
        错误日志器.error(
            f"获取LangChain系统配置API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1519, f"获取LangChain系统配置失败: {str(e)}")


@管理LangChain智能体路由.post("/system/config/update", summary="更新LangChain系统配置")
async def 更新LangChain系统配置(
    配置数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    更新LangChain系统配置
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求更新LangChain系统配置")

        # 验证配置数据
        允许更新的配置项 = {
            "最大并发智能体数",
            "会话超时时间",
            "记忆窗口默认大小",
            "默认温度",
            "默认最大令牌数",
            "缓存过期时间",
            "请求超时",
            "重试次数",
        }

        更新的配置项 = []
        for 配置项, 配置值 in 配置数据.items():
            if 配置项 in 允许更新的配置项:
                更新的配置项.append(配置项)
                # 这里可以添加实际的配置更新逻辑
                路由日志器.info(f"配置项 {配置项} 更新为: {配置值}")

        更新结果 = {
            "更新时间": datetime.now().isoformat(),
            "更新人": 管理员.get("用户名", "管理员"),
            "更新的配置项": 更新的配置项,
            "更新数量": len(更新的配置项),
        }

        路由日志器.info(
            f"管理员 {管理员['id']} 更新LangChain系统配置成功，更新了 {len(更新的配置项)} 个配置项"
        )
        return 统一响应模型.成功(更新结果, "LangChain系统配置更新成功")

    except Exception as e:
        错误日志器.error(
            f"更新LangChain系统配置API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1520, f"更新LangChain系统配置失败: {str(e)}")


@管理LangChain智能体路由.post("/system/logs", summary="获取LangChain系统日志")
async def 获取LangChain系统日志(
    查询参数: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取LangChain系统日志
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取LangChain系统日志")

        # 解析查询参数
        日志级别 = 查询参数.get("日志级别", "INFO")
        开始时间 = 查询参数.get("开始时间")
        结束时间 = 查询参数.get("结束时间")
        页码 = 查询参数.get("页码", 1)
        每页数量 = 查询参数.get("每页数量", 50)

        # 模拟日志数据（实际项目中应该从日志文件或数据库读取）
        模拟日志 = [
            {
                "时间": "2024-01-01 10:00:00",
                "级别": "INFO",
                "模块": "LangChain.智能体工厂",
                "消息": "智能体工厂初始化完成",
                "详情": "成功加载 5 个智能体配置",
            },
            {
                "时间": "2024-01-01 10:01:00",
                "级别": "INFO",
                "模块": "LangChain.模型管理器",
                "消息": "模型管理器初始化完成",
                "详情": "成功连接到阿里云百炼API",
            },
            {
                "时间": "2024-01-01 10:02:00",
                "级别": "DEBUG",
                "模块": "LangChain.对话处理",
                "消息": "处理用户对话请求",
                "详情": "智能体id: agent_001, 用户id: user_123",
            },
        ]

        # 根据级别过滤
        if 日志级别 != "ALL":
            模拟日志 = [log for log in 模拟日志 if log["级别"] == 日志级别]

        # 分页处理
        开始索引 = (页码 - 1) * 每页数量
        结束索引 = 开始索引 + 每页数量
        分页日志 = 模拟日志[开始索引:结束索引]

        日志结果 = {
            "日志列表": 分页日志,
            "总数量": len(模拟日志),
            "当前页码": 页码,
            "每页数量": 每页数量,
            "总页数": (len(模拟日志) + 每页数量 - 1) // 每页数量,
            "查询时间": datetime.now().isoformat(),
            "查询参数": {
                "日志级别": 日志级别,
                "开始时间": 开始时间,
                "结束时间": 结束时间,
            },
        }

        路由日志器.info(
            f"管理员 {管理员['id']} 获取LangChain系统日志成功，返回 {len(分页日志)} 条记录"
        )
        return 统一响应模型.成功(日志结果, "获取LangChain系统日志成功")

    except Exception as e:
        错误日志器.error(
            f"获取LangChain系统日志API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1521, f"获取LangChain系统日志失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/assign", summary="分配智能体给用户")
async def 分配智能体给用户(
    请求数据: 用户智能体分配请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    将智能体分配给指定用户

    - **用户表id列表**: 要分配的用户id列表
    - **分配类型**: individual(个人) 或 shared(共享)
    - **备注**: 分配备注信息
    """
    try:
        # 调用服务层进行分配
        分配结果 = await LangChain智能体服务实例.分配智能体给用户详细(
            智能体id=请求数据.智能体id,
            用户id列表=请求数据.用户id列表,
            权限级别=请求数据.权限级别,
            分配类型=请求数据.分配类型,
            备注=请求数据.备注 or "",
        )

        if 分配结果.get("success"):
            成功数量 = len(分配结果["成功分配"])
            失败数量 = len(分配结果["失败分配"])
            路由日志器.info(
                f"智能体分配完成 - 管理员: {管理员['id']}, 智能体: {请求数据.智能体id}, 成功: {成功数量}, 失败: {失败数量}"
            )

            return 统一响应模型.成功(
                {
                    "智能体id": 请求数据.智能体id,
                    "智能体名称": 分配结果["智能体信息"]["智能体名称"],
                    "成功分配": 分配结果["成功分配"],
                    "失败分配": 分配结果["失败分配"],
                    "分配时间": datetime.now().isoformat(),
                },
                f"智能体分配完成，成功: {成功数量}, 失败: {失败数量}",
            )
        else:
            return 统一响应模型.失败(1518, 分配结果.get("error", "分配智能体失败"))

    except Exception as e:
        错误日志器.error(
            f"分配智能体给用户API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1518, f"分配智能体失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/unassign", summary="取消分配智能体")
async def 取消分配智能体(
    请求数据: 用户智能体取消分配请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    取消智能体与用户的分配关系
    """
    try:
        # 调用服务层进行取消分配
        取消结果 = await LangChain智能体服务实例.取消智能体分配批量(
            智能体id=请求数据.智能体id, 用户id列表=请求数据.用户表id列表
        )

        if 取消结果.get("success"):
            # 重新加载智能体配置
            await LangChain智能体服务实例.重新加载智能体(str(请求数据.智能体id))

            成功数量 = len(取消结果["成功取消"])
            失败数量 = len(取消结果["失败取消"])
            路由日志器.info(
                f"智能体取消分配完成 - 管理员: {管理员['id']}, 智能体: {请求数据.智能体id}, 成功: {成功数量}, 失败: {失败数量}"
            )

            return 统一响应模型.成功(
                {
                    "智能体id": 请求数据.智能体id,
                    "成功取消": 取消结果["成功取消"],
                    "失败取消": 取消结果["失败取消"],
                    "取消时间": datetime.now().isoformat(),
                },
                f"取消分配完成，成功: {成功数量}, 失败: {失败数量}",
            )
        else:
            return 统一响应模型.失败(1519, 取消结果.get("error", "取消分配智能体失败"))

    except Exception as e:
        错误日志器.error(
            f"取消分配智能体API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1519, f"取消分配智能体失败: {str(e)}")


@管理LangChain智能体路由.put("/agents/{智能体id}/enable", summary="切换智能体启用状态")
async def 切换智能体启用状态(
    智能体id: int,
    是否启用: bool = Body(..., description="是否启用智能体"),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    切换智能体启用状态

    - **是否启用**: true 启用, false 禁用
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求切换智能体启用状态: {智能体id} -> {是否启用}"
        )

        # 更新数据库中的启用状态
        更新结果 = await LangChain智能体服务实例.更新智能体启用状态(智能体id, 是否启用)

        if 更新结果.get("success"):
            路由日志器.info(
                f"智能体启用状态更新成功 - 管理员: {管理员['id']}, 智能体: {智能体id}, 新状态: {是否启用}"
            )
            return 统一响应模型.成功(
                {
                    "智能体id": 智能体id,
                    "是否启用": 是否启用,
                    "更新时间": datetime.now().isoformat(),
                },
                f"智能体已{'启用' if 是否启用 else '禁用'}",
            )
        else:
            return 统一响应模型.失败(
                1522, 更新结果.get("error", "更新智能体启用状态失败")
            )

    except Exception as e:
        错误日志器.error(
            f"切换智能体启用状态API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1523, f"切换智能体启用状态失败: {str(e)}")


@管理LangChain智能体路由.post("/agents/users", summary="获取智能体分配的用户列表")
async def 获取智能体分配用户(
    请求数据: 智能体分配用户查询请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    获取智能体分配的用户列表
    """
    try:
        # 验证智能体是否存在
        验证结果 = await LangChain智能体服务实例.验证智能体存在(请求数据.智能体id)
        if not 验证结果.get("success"):
            return 统一响应模型.失败(1404, 验证结果.get("error", "智能体不存在"))

        智能体信息 = 验证结果["智能体信息"]

        # 调用服务层获取分配用户详细信息
        分配用户结果 = await LangChain智能体服务实例.获取智能体分配用户详细(
            请求数据.智能体id
        )

        if 分配用户结果.get("success"):
            return 统一响应模型.成功(
                {
                    "智能体id": 请求数据.智能体id,
                    "智能体名称": 智能体信息["智能体名称"],
                    "分配用户列表": 分配用户结果["分配用户列表"],
                    "总用户数": 分配用户结果["总用户数"],
                },
                "获取智能体分配用户成功",
            )
        else:
            return 统一响应模型.失败(
                1520, f"获取智能体分配用户失败: {分配用户结果.get('error', '未知错误')}"
            )

    except Exception as e:
        错误日志器.error(
            f"获取智能体分配用户API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1520, f"获取智能体分配用户失败: {str(e)}")


# ==================== LangChain知识库管理接口 ====================


class LangChain知识库创建请求模型(BaseModel):
    """LangChain知识库创建请求模型"""

    知识库名称: str = Field(..., description="知识库名称", min_length=1, max_length=100)
    知识库描述: Optional[str] = Field("", description="知识库描述")
    嵌入模型id: Optional[int] = Field(None, description="嵌入模型id（模型配置表id）")
    向量维度: Optional[int] = Field(1024, description="向量维度", ge=64, le=2048)
    是否公开: Optional[bool] = Field(False, description="是否公开知识库")
    # MCP工具配置移除 - 应该在智能体配置中，不属于知识库

    # 兼容性字段已移除 - 现在使用独立字段架构


class LangChain知识库列表查询请求模型(BaseModel):
    """LangChain知识库列表查询请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(10, description="每页数量", ge=1, le=100)
    搜索关键词: Optional[str] = Field(None, description="搜索关键词")
    知识库类型: Optional[str] = Field(None, description="知识库类型筛选")
    是否公开: Optional[bool] = Field(None, description="是否公开筛选")


@管理LangChain智能体路由.post("/knowledge/list", summary="获取LangChain知识库列表")
async def 获取LangChain知识库列表(
    请求数据: LangChain知识库列表查询请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    获取LangChain知识库列表（管理员视图）
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取LangChain知识库列表")

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "搜索关键词": 请求数据.搜索关键词,
            "知识库类型": 请求数据.知识库类型,
            "是否公开": 请求数据.是否公开,
        }

        # 调用知识库服务层获取数据
        结果 = await LangChain知识库服务实例.获取知识库列表(查询参数)

        if 结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 获取LangChain知识库列表成功，返回 {结果.get('总数量', 0)} 条记录"
            )
            return 统一响应模型.成功(结果, "获取LangChain知识库列表成功")
        else:
            return 统一响应模型.失败(1501, 结果.get("error", "获取知识库列表失败"))

    except Exception as e:
        错误日志器.error(
            f"获取LangChain知识库列表API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1521, f"获取LangChain知识库列表失败: {str(e)}")


# ==================== 文档管理接口 ====================


@管理LangChain智能体路由.post("/knowledge/{knowledge_base_id}/documents/list")
async def 获取知识库文档列表(
    knowledge_base_id: int,
    请求数据: 知识库文档列表请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """获取知识库文档列表"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求获取知识库文档列表: {knowledge_base_id}"
        )

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "搜索关键字": 请求数据.搜索关键字,
            "文件类型": 请求数据.文件类型,
            "处理状态": 请求数据.处理状态,
        }

        # 调用知识库服务层获取数据
        结果 = await LangChain知识库服务实例.获取知识库文档列表(
            knowledge_base_id, 查询参数
        )

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 获取知识库文档列表成功: {knowledge_base_id}"
            )

            # 提取实际数据
            响应数据 = {
                "文档列表": 结果["文档列表"],
                "总数量": 结果["总数量"],
                "页码": 结果["页码"],
                "每页数量": 结果["每页数量"],
                "总页数": 结果["总页数"],
            }

            return 统一响应模型.成功(响应数据, "获取知识库文档列表成功")
        else:
            return 统一响应模型.失败(1524, 结果["error"])

    except Exception as e:
        错误日志器.error(
            f"获取知识库文档列表API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1524, f"获取知识库文档列表失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/{knowledge_base_id}/documents/upload")
async def 批量上传文档(
    knowledge_base_id: int,
    files: List[UploadFile] = File(...),
    auto_process: str = Form("true"),
    chunk_strategy: str = Form("智能递归分块"),
    chunk_size: int = Form(1000),
    chunk_overlap: int = Form(200),
    管理员: dict = Depends(获取当前管理员用户),
):
    """批量上传文档到知识库 - 优化版本"""
    import os
    import shutil
    import tempfile
    from pathlib import Path

    import aiofiles

    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求批量上传文档到知识库: {knowledge_base_id}, "
            f"文件数量: {len(files)}, 分块策略: {chunk_strategy}, "
            f"分块大小: {chunk_size}, 分块重叠: {chunk_overlap}, 自动处理: {auto_process}"
        )

        # 文件大小限制配置 (100MB)
        最大文件大小 = 100 * 1024 * 1024  # 100MB

        # 验证文件大小
        for file in files:
            if file.size > 最大文件大小:
                return 统一响应模型.失败(
                    413, f"文件 {file.filename} 过大，最大支持100MB"
                )

        # 验证知识库是否存在
        # 知识库服务已使用简化初始化模式

        知识库详情 = await LangChain知识库服务实例.获取知识库详情(knowledge_base_id)
        if not 知识库详情.get("success"):
            return 统一响应模型.失败(1526, f"知识库不存在: {knowledge_base_id}")

        # 创建知识库专用的文档存储目录
        知识库文档目录 = Path(f"./uploads/knowledge_base_{knowledge_base_id}/documents")
        知识库文档目录.mkdir(parents=True, exist_ok=True)

        上传结果列表 = []
        上传成功数量 = 0
        上传失败数量 = 0
        处理的文档列表 = []

        # 创建临时目录用于文件验证
        temp_dir = tempfile.mkdtemp()

        try:
            # 第一阶段：文件验证和保存
            for file in files:
                try:
                    # 文件基础验证
                    if not file.filename:
                        上传结果列表.append(
                            {
                                "文件名": "未知文件",
                                "状态": "验证失败",
                                "错误信息": "文件名为空",
                            }
                        )
                        上传失败数量 += 1
                        continue

                    # 文件大小验证
                    if file.size > 100 * 1024 * 1024:  # 100MB限制
                        上传结果列表.append(
                            {
                                "文件名": file.filename,
                                "状态": "验证失败",
                                "错误信息": "文件大小超过100MB限制",
                            }
                        )
                        上传失败数量 += 1
                        continue

                    # 文件格式验证
                    支持的格式 = [
                        ".txt",
                        ".pdf",
                        ".docx",
                        ".doc",
                        ".md",
                        ".xlsx",
                        ".xls",
                        ".pptx",
                        ".ppt",
                        ".html",
                        ".htm",
                        ".xml",
                        ".rtf",
                        ".odt",
                        ".csv",
                        ".json",
                        # 图片格式
                        ".jpg",
                        ".jpeg",
                        ".png",
                        ".gif",
                        ".bmp",
                        ".webp",
                    ]
                    文件扩展名 = Path(file.filename).suffix.lower()
                    if 文件扩展名 not in 支持的格式:
                        上传结果列表.append(
                            {
                                "文件名": file.filename,
                                "状态": "验证失败",
                                "错误信息": f"不支持的文件格式: {文件扩展名}",
                            }
                        )
                        上传失败数量 += 1
                        continue

                    # 生成唯一文件名避免冲突
                    import uuid
                    from datetime import datetime

                    时间戳 = datetime.now().strftime("%Y%m%d_%H%M%S")
                    唯一标识 = str(uuid.uuid4())[:8]
                    安全文件名 = f"{时间戳}_{唯一标识}_{file.filename}"

                    # 保存到临时目录进行验证
                    temp_file_path = os.path.join(temp_dir, 安全文件名)
                    async with aiofiles.open(temp_file_path, "wb") as temp_file:
                        content = await file.read()
                        await temp_file.write(content)

                    # 验证文件内容不为空
                    if os.path.getsize(temp_file_path) == 0:
                        上传结果列表.append(
                            {
                                "文件名": file.filename,
                                "状态": "验证失败",
                                "错误信息": "文件内容为空",
                            }
                        )
                        上传失败数量 += 1
                        continue

                    # 移动到正式存储目录
                    正式文件路径 = 知识库文档目录 / 安全文件名
                    shutil.move(temp_file_path, 正式文件路径)

                    处理的文档列表.append(
                        {
                            "原始文件名": file.filename,
                            "安全文件名": 安全文件名,
                            "文件路径": str(正式文件路径),
                            "文件大小": file.size,
                        }
                    )

                    路由日志器.debug(f"文件验证通过: {file.filename} -> {安全文件名}")

                except Exception as e:
                    路由日志器.error(f"文件处理失败 {file.filename}: {str(e)}")
                    上传结果列表.append(
                        {
                            "文件名": file.filename,
                            "状态": "处理失败",
                            "错误信息": str(e),
                        }
                    )
                    上传失败数量 += 1

            # 第二阶段：文档处理和向量化
            for 文档信息 in 处理的文档列表:
                try:
                    # 调用知识库服务层处理文档 - 使用优化版本
                    处理结果 = await LangChain知识库服务实例.上传文档到知识库_优化版本(
                        knowledge_base_id,
                        文档信息["文件路径"],
                        文档信息["原始文件名"],
                        chunk_strategy,
                        chunk_size,
                        chunk_overlap,
                    )

                    if 处理结果["success"]:
                        上传结果列表.append(
                            {
                                "文件名": 文档信息["原始文件名"],
                                "文件大小": 文档信息["文件大小"],
                                "状态": "上传成功",
                                "文档id": 处理结果["文档id"],
                                "文档名称": 处理结果["文档名称"],
                                "文档类型": 处理结果["文档类型"],
                                "分块数量": 处理结果["分块数量"],
                                "向量化状态": 处理结果.get("向量化状态", "已向量化"),
                                "分块配置": {
                                    "分块策略": chunk_strategy,
                                    "分块大小": chunk_size,
                                    "分块重叠": chunk_overlap,
                                },
                                "处理状态": "已处理",
                            }
                        )
                        上传成功数量 += 1
                    else:
                        上传结果列表.append(
                            {
                                "文件名": 文档信息["原始文件名"],
                                "状态": "上传失败",
                                "错误信息": 处理结果.get("error", "未知错误"),
                            }
                        )
                        上传失败数量 += 1

                    # 清理临时文件
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)

                except Exception as file_error:
                    路由日志器.error(
                        f"处理文件 {文档信息['原始文件名']} 时出错: {str(file_error)}"
                    )
                    上传结果列表.append(
                        {
                            "文件名": 文档信息["原始文件名"],
                            "状态": "上传失败",
                            "错误信息": str(file_error),
                        }
                    )
                    上传失败数量 += 1

        finally:
            # 清理临时目录
            try:
                os.rmdir(temp_dir)
            except OSError:
                pass

        路由日志器.info(
            f"管理员 {管理员['id']} 批量上传文档完成: 成功 {上传成功数量}, 失败 {上传失败数量}"
        )

        return 统一响应模型.成功(
            {
                "成功数量": 上传成功数量,
                "失败数量": 上传失败数量,
                "总数量": len(files),
                "上传结果列表": 上传结果列表,
            },
            "批量上传文档完成",
        )

    except Exception as e:
        错误日志器.error(
            f"批量上传文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1525, f"批量上传文档失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/{knowledge_base_id}/search")
async def 向量检索(
    knowledge_base_id: int,
    检索请求: dict = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """向量检索接口"""
    try:
        查询文本 = 检索请求.get("查询文本", "")
        最大数量 = 检索请求.get("最大数量", 10)
        检索方式 = 检索请求.get("检索方式", "postgresql")  # postgresql 或 mixed
        相似度阈值 = 检索请求.get("相似度阈值", 0.5)  # 默认阈值0.5
        查询优化配置 = 检索请求.get("查询优化配置")  # 可选的查询优化配置

        if not 查询文本.strip():
            return 统一响应模型.失败(1400, "查询文本不能为空")

        路由日志器.info(
            f"管理员 {管理员['id']} 请求向量检索: 知识id {knowledge_base_id}, "
            f"查询: '{查询文本}', 方式: {检索方式}, 相似度阈值: {相似度阈值}"
        )

        # 根据检索方式选择不同的检索方法
        if 检索方式 == "mixed":
            检索结果 = await LangChain知识库服务实例.混合检索(
                knowledge_base_id, 查询文本, 最大数量, 相似度阈值
            )
        else:
            检索结果 = await LangChain知识库服务实例.PostgreSQL向量检索(
                knowledge_base_id, 查询文本, 最大数量, 相似度阈值, 查询优化配置
            )

        if 检索结果.get("success"):
            路由日志器.info(f"向量检索成功: 返回 {检索结果.get('结果数量', 0)} 个结果")
            return 统一响应模型.成功(检索结果, "向量检索成功")
        else:
            路由日志器.warning(f"向量检索失败: {检索结果.get('error')}")
            return 统一响应模型.失败(1500, 检索结果.get("error", "向量检索失败"))

    except Exception as e:
        错误日志器.error(f"向量检索API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1500, f"向量检索失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/documents/{document_id}/delete")
async def 删除知识库文档(document_id: str, 管理员: dict = Depends(获取当前管理员用户)):
    """删除知识库文档"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求删除知识库文档: {document_id}")

        # 调用知识库服务层删除文档（智能识别ID类型）
        try:
            文档记录ID = int(document_id)
            结果 = await LangChain知识库服务实例.通过ID删除知识库文档(文档记录ID)
        except (ValueError, TypeError):
            # 如果转换失败，可能是UUID，尝试通过UUID删除
            结果 = await LangChain知识库服务实例.通过UUID删除知识库文档(document_id)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 删除知识库文档成功: {document_id}")
            return 统一响应模型.成功({"文档id": document_id}, "删除知识库文档成功")
        else:
            return 统一响应模型.失败(1525, 结果.get("error", "删除知识库文档失败"))

    except Exception as e:
        错误日志器.error(
            f"删除知识库文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1525, f"删除知识库文档失败: {str(e)}")


@管理LangChain智能体路由.get("/knowledge/supported-formats")
async def 获取支持的文件格式(管理员: dict = Depends(获取当前管理员用户)):
    """获取支持的文件格式"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取支持的文件格式")

        # 调用服务层获取支持的文件格式
        结果 = await LangChain智能体服务实例.获取支持的文件格式()

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 获取支持的文件格式成功")
            return 统一响应模型.成功(结果, "获取支持的文件格式成功")
        else:
            return 统一响应模型.失败(1526, 结果.get("error", "获取支持的文件格式失败"))

    except Exception as e:
        错误日志器.error(
            f"获取支持的文件格式API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1526, f"获取支持的文件格式失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/knowledge/embedding-models", summary="获取可用嵌入模型列表"
)
async def 获取可用嵌入模型列表(管理员: dict = Depends(获取当前管理员用户)):
    """获取可用嵌入模型列表"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取可用嵌入模型列表")

        # 调用知识库服务层获取嵌入模型列表
        结果 = await LangChain知识库服务实例.获取可用嵌入模型列表()

        if 结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 获取可用嵌入模型列表成功，返回 {len(结果.get('data', []))} 个模型"
            )
            return 统一响应模型.成功(结果["data"], "获取可用嵌入模型列表成功")
        else:
            return 统一响应模型.失败(
                1501, 结果.get("error", "获取可用嵌入模型列表失败")
            )

    except Exception as e:
        错误日志器.error(
            f"获取可用嵌入模型列表API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1501, f"获取可用嵌入模型列表失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/ai-models", summary="获取可用AI模型列表")
async def 获取可用AI模型列表(管理员: dict = Depends(获取当前管理员用户)):
    """获取可用AI模型列表"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取可用AI模型列表")

        # 调用知识库服务层获取AI模型列表
        结果 = await LangChain知识库服务实例.获取可用AI模型列表()

        if 结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 获取可用AI模型列表成功，返回 {len(结果.get('data', []))} 个模型"
            )
            return 统一响应模型.成功(结果["data"], "获取可用AI模型列表成功")
        else:
            return 统一响应模型.失败(1501, 结果.get("error", "获取可用AI模型列表失败"))

    except Exception as e:
        错误日志器.error(
            f"获取可用AI模型列表API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1501, f"获取可用AI模型列表失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/create", summary="创建LangChain知识库")
async def 创建LangChain知识库(
    请求数据: LangChain知识库创建请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    创建LangChain知识库（管理员操作）
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求创建LangChain知识库: {请求数据.知识库名称}"
        )

        # 构建知识库数据 - 简化字段映射
        知识库数据 = {
            "知识库名称": 请求数据.知识库名称,
            "知识库描述": 请求数据.知识库描述,
            "用户表id": 管理员["id"],  # 修正字段名
            "嵌入模型id": 请求数据.嵌入模型id,
            "向量维度": 请求数据.向量维度,
            "是否公开": 请求数据.是否公开,
        }

        # 移除兼容性处理 - 现在只使用新的字段结构

        # 调用知识库服务层创建知识库
        结果 = await LangChain知识库服务实例.创建知识库(知识库数据)

        if 结果.get("success"):
            知识id = 结果.get("data", {}).get("知识id")
            路由日志器.info(
                f"管理员 {管理员['id']} 创建LangChain知识库成功: {请求数据.知识库名称} (ID: {知识id})"
            )
            return 统一响应模型.成功(结果.get("data"), "LangChain知识库创建成功")
        else:
            return 统一响应模型.失败(1522, 结果.get("error", "创建LangChain知识库失败"))

    except Exception as e:
        错误日志器.error(
            f"创建LangChain知识库API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1522, f"创建LangChain知识库失败: {str(e)}")


# 原接口已移至POST方法，保持向后兼容


@管理LangChain智能体路由.post(
    "/knowledge/{knowledge_base_id}/clone", summary="克隆知识库"
)
async def 克隆知识库(
    knowledge_base_id: int,
    请求数据: LangChain知识库克隆请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """克隆知识库"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求克隆知识库: {knowledge_base_id} -> {请求数据.新知识库名称}"
        )

        # 调用知识库服务层克隆
        结果 = await LangChain知识库服务实例.克隆知识库(
            knowledge_base_id, 请求数据.新知识库名称, 管理员["id"]
        )

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 克隆知识库成功: {knowledge_base_id} -> {结果['知识id']}"
            )
            return 统一响应模型.成功(结果, "克隆知识库成功")
        else:
            return 统一响应模型.失败(1530, 结果.get("error", "克隆知识库失败"))

    except Exception as e:
        错误日志器.error(
            f"克隆知识库API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1530, f"克隆知识库失败: {str(e)}")


# ==================== 向量化管理接口 ====================


@管理LangChain智能体路由.post(
    "/knowledge/{knowledge_base_id}/vectorize", summary="批量向量化知识库文档"
)
async def 向量化知识库(
    knowledge_base_id: int, 管理员: dict = Depends(获取当前管理员用户)
):
    """批量向量化知识库中的所有文档"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求批量向量化知识库文档: {knowledge_base_id}"
        )

        # 调用知识库服务层向量化
        结果 = await LangChain知识库服务实例.向量化知识库(knowledge_base_id)

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 开始向量化知识库: {knowledge_base_id}"
            )
            return 统一响应模型.成功(结果, "开始向量化知识库")
        else:
            return 统一响应模型.失败(1531, 结果.get("error", "向量化知识库失败"))

    except Exception as e:
        错误日志器.error(
            f"向量化知识库API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1531, f"向量化知识库失败: {str(e)}")


@管理LangChain智能体路由.get(
    "/knowledge/{knowledge_base_id}/vector_status", summary="获取向量化状态"
)
async def 获取向量化状态(
    knowledge_base_id: int, 管理员: dict = Depends(获取当前管理员用户)
):
    """获取向量化状态"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求获取向量化状态: {knowledge_base_id}"
        )

        # 调用知识库服务层获取状态
        结果 = await LangChain知识库服务实例.获取向量化状态(knowledge_base_id)

        if 结果["success"]:
            return 统一响应模型.成功(结果, "获取向量化状态成功")
        else:
            return 统一响应模型.失败(1532, 结果.get("error", "获取向量化状态失败"))

    except Exception as e:
        错误日志器.error(
            f"获取向量化状态API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1532, f"获取向量化状态失败: {str(e)}")


# ==================== 文档详细管理接口 ====================


@管理LangChain智能体路由.get("/documents/{document_id}", summary="获取文档详情")
async def 获取文档详情(document_id: str, 管理员: dict = Depends(获取当前管理员用户)):
    """获取文档详情"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取文档详情: {document_id}")

        # 调用知识库服务层获取详情（通过ID）
        try:
            文档记录ID = int(document_id)
            结果 = await LangChain知识库服务实例.通过ID获取文档详情(文档记录ID)
        except (ValueError, TypeError):
            # 如果转换失败，可能是UUID，尝试通过UUID获取
            结果 = await LangChain知识库服务实例.通过UUID获取文档详情(document_id)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 获取文档详情成功: {document_id}")
            return 统一响应模型.成功(结果["data"], "获取文档详情成功")
        else:
            return 统一响应模型.失败(1533, 结果.get("error", "获取文档详情失败"))

    except Exception as e:
        错误日志器.error(
            f"获取文档详情API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1533, f"获取文档详情失败: {str(e)}")


@管理LangChain智能体路由.put("/documents/{document_id}", summary="更新文档")
async def 更新文档(
    document_id: str,
    请求数据: 文档更新请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """更新文档"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求更新文档: {document_id}")

        # 构建更新数据
        更新数据 = {}
        if 请求数据.文档名称 is not None:
            更新数据["文档名称"] = 请求数据.文档名称
        if 请求数据.文档内容 is not None:
            更新数据["文档内容"] = 请求数据.文档内容
        if 请求数据.状态 is not None:
            更新数据["状态"] = 请求数据.状态
        if 请求数据.元数据 is not None:
            更新数据["元数据"] = 请求数据.元数据

        # 调用知识库服务层更新文档（智能识别ID类型）
        try:
            文档记录ID = int(document_id)
            结果 = await LangChain知识库服务实例.通过ID更新文档(文档记录ID, 更新数据)
        except (ValueError, TypeError):
            # 如果转换失败，可能是UUID，尝试通过UUID更新
            结果 = await LangChain知识库服务实例.通过UUID更新文档(document_id, 更新数据)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 更新文档成功: {document_id}")
            return 统一响应模型.成功(结果, "更新文档成功")
        else:
            return 统一响应模型.失败(1534, 结果.get("error", "更新文档失败"))

    except Exception as e:
        错误日志器.error(
            f"更新文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1534, f"更新文档失败: {str(e)}")


@管理LangChain智能体路由.post("/documents/batch_delete", summary="批量删除文档")
async def 批量删除文档(
    请求数据: 批量删除文档请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """批量删除文档"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求批量删除文档: {len(请求数据.文档id列表)} 个"
        )

        # 调用知识库服务层批量删除文档
        结果 = await LangChain知识库服务实例.批量删除文档(请求数据.文档id列表)

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 批量删除文档成功: {结果['成功数量']}/{结果['总数量']}"
            )
            return 统一响应模型.成功(结果, "批量删除文档成功")
        else:
            return 统一响应模型.失败(1535, 结果.get("error", "批量删除文档失败"))

    except Exception as e:
        错误日志器.error(
            f"批量删除文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1535, f"批量删除文档失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/knowledge/{knowledge_base_id}/documents/search", summary="搜索文档"
)
async def 搜索文档(
    knowledge_base_id: int,
    请求数据: 文档搜索请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """在知识库中搜索文档"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求搜索文档: 知识库 {knowledge_base_id}, 关键词: {请求数据.搜索关键词}"
        )

        # 调用知识库服务层搜索文档
        结果 = await LangChain知识库服务实例.搜索文档(
            knowledge_base_id, 请求数据.搜索关键词, 请求数据.页码, 请求数据.每页数量
        )

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 搜索文档成功: 找到 {len(结果['文档列表'])} 个结果"
            )
            return 统一响应模型.成功(结果, "搜索文档成功")
        else:
            return 统一响应模型.失败(1536, 结果.get("error", "搜索文档失败"))

    except Exception as e:
        错误日志器.error(
            f"搜索文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1536, f"搜索文档失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/documents/{document_id}/reprocess", summary="重新处理文档"
)
async def 重新处理文档(document_id: str, 管理员: dict = Depends(获取当前管理员用户)):
    """重新处理文档"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求重新处理文档: {document_id}")

        # 调用知识库服务层重新处理文档（智能识别ID类型）
        try:
            文档记录ID = int(document_id)
            结果 = await LangChain知识库服务实例.通过ID重新处理文档(文档记录ID)
        except (ValueError, TypeError):
            # 如果转换失败，可能是UUID，尝试通过UUID重新处理
            结果 = await LangChain知识库服务实例.通过UUID重新处理文档(document_id)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 开始重新处理文档: {document_id}")
            return 统一响应模型.成功(结果, "开始重新处理文档")
        else:
            return 统一响应模型.失败(1537, 结果.get("error", "重新处理文档失败"))

    except Exception as e:
        错误日志器.error(
            f"重新处理文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1537, f"重新处理文档失败: {str(e)}")


# ==================== 检索配置管理接口 ====================


@管理LangChain智能体路由.get(
    "/knowledge/{knowledge_base_id}/retrieval_config", summary="获取检索配置"
)
async def 获取检索配置(
    knowledge_base_id: int, 管理员: dict = Depends(获取当前管理员用户)
):
    """获取知识库检索配置"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取检索配置: {knowledge_base_id}")

        # 调用知识库服务层获取配置
        结果 = await LangChain知识库服务实例.获取检索配置(knowledge_base_id)

        if 结果["success"]:
            return 统一响应模型.成功(结果["data"], "获取检索配置成功")
        else:
            return 统一响应模型.失败(1538, 结果.get("error", "获取检索配置失败"))

    except Exception as e:
        错误日志器.error(
            f"获取检索配置API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1538, f"获取检索配置失败: {str(e)}")


@管理LangChain智能体路由.put(
    "/knowledge/{knowledge_base_id}/retrieval_config", summary="更新检索配置"
)
async def 更新检索配置(
    knowledge_base_id: int,
    请求数据: 检索配置请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """更新知识库检索配置"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求更新检索配置: {knowledge_base_id}")

        # 构建配置数据
        配置数据 = {}
        if 请求数据.检索策略 is not None:
            配置数据["检索策略"] = 请求数据.检索策略
        if 请求数据.相似度阈值 is not None:
            配置数据["相似度阈值"] = 请求数据.相似度阈值
        if 请求数据.最大检索数量 is not None:
            配置数据["最大检索数量"] = 请求数据.最大检索数量
        if 请求数据.分块大小 is not None:
            配置数据["分块大小"] = 请求数据.分块大小
        if 请求数据.分块重叠 is not None:
            配置数据["分块重叠"] = 请求数据.分块重叠
        if 请求数据.分块策略 is not None:
            配置数据["分块策略"] = 请求数据.分块策略

        # 调用服务层更新配置
        结果 = await LangChain智能体服务实例.更新检索配置(knowledge_base_id, 配置数据)

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 更新检索配置成功: {knowledge_base_id}"
            )
            return 统一响应模型.成功(结果, "更新检索配置成功")
        else:
            return 统一响应模型.失败(1539, 结果.get("error", "更新检索配置失败"))

    except Exception as e:
        错误日志器.error(
            f"更新检索配置API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1539, f"更新检索配置失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/knowledge/{knowledge_base_id}/test_retrieval", summary="测试检索（旧版本）"
)
async def 测试检索(
    knowledge_base_id: int,
    请求数据: 检索测试请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """测试检索（旧版本，保持向后兼容）"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求测试检索: 知识库 {knowledge_base_id}, 查询: {请求数据.查询文本}"
        )

        # 构建检索参数，包含嵌入模型id
        检索参数 = 请求数据.检索参数 or {}
        if 请求数据.嵌入模型id:
            检索参数["嵌入模型id"] = 请求数据.嵌入模型id

        # 调用知识库服务层进行直接知识库检索
        结果 = await LangChain知识库服务实例.直接知识库检索(
            knowledge_base_id, 请求数据.查询文本, 检索参数
        )

        if 结果["success"]:
            检索数据 = 结果.get("data", {})
            检索结果数量 = len(检索数据.get("检索结果", []))
            路由日志器.info(
                f"管理员 {管理员['id']} 测试检索成功: 找到 {检索结果数量} 个结果"
            )
            return 统一响应模型.成功(检索数据, "测试检索成功")
        else:
            return 统一响应模型.失败(1538, 结果.get("error", "测试检索失败"))

    except Exception as e:
        错误日志器.error(
            f"测试检索API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1538, f"测试检索失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/knowledge/test_retrieval", summary="知识库检索测试（新版本）"
)
async def 知识库检索测试(
    请求数据: 知识库检索测试请求模型,
    管理员: dict = Depends(获取当前管理员用户),
):
    """知识库检索测试（新版本） - 支持多知识库和完整参数配置"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求知识库检索测试: 知识库列表 {请求数据.知识库列表}, 查询: {请求数据.测试查询}"
        )

        # 调用知识库服务层进行实时参数检索测试
        结果 = await LangChain知识库服务实例.实时参数检索测试(
            知识库列表=请求数据.知识库列表,
            测试查询=请求数据.测试查询,
            检索配置=请求数据.检索配置,
            查询优化配置=请求数据.查询优化配置,
            测试模式=请求数据.测试模式,
        )

        if 结果["success"]:
            检索数据 = 结果.get("data", {})
            总结果数量 = len(检索数据.get("检索结果", []))
            路由日志器.info(
                f"管理员 {管理员['id']} 知识库检索测试成功: 总共找到 {总结果数量} 个结果"
            )
            return 统一响应模型.成功(检索数据, "知识库检索测试成功")
        else:
            return 统一响应模型.失败(1538, 结果.get("error", "知识库检索测试失败"))

    except Exception as e:
        错误日志器.error(
            f"知识库检索测试API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1538, f"知识库检索测试失败: {str(e)}")


@管理LangChain智能体路由.post("/agent/test_retrieval", summary="测试智能体检索")
async def 测试智能体检索(
    请求数据: 智能体检索测试请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """测试智能体检索功能 - 智能体通过RAG引擎检索关联知识库"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求测试智能体检索: 智能体 {请求数据.智能体id}, 查询: {请求数据.查询文本}"
        )

        # 构建检索参数，包含嵌入模型id和手动优化查询配置
        检索参数 = 请求数据.检索参数 or {}
        if 请求数据.嵌入模型id:
            检索参数["嵌入模型id"] = 请求数据.嵌入模型id

        # 添加手动优化查询配置
        if 请求数据.手动优化查询:
            检索参数["手动优化查询"] = 请求数据.手动优化查询
            检索参数["跳过第一阶段"] = 请求数据.跳过第一阶段
            路由日志器.info(
                f"手动优化查询: '{请求数据.手动优化查询}', 跳过第一阶段: {请求数据.跳过第一阶段}"
            )

        # 调用知识库服务层进行智能体检索测试
        结果 = await LangChain知识库服务实例.智能体检索测试(
            请求数据.智能体id, 请求数据.查询文本, 检索参数
        )

        if 结果["success"]:
            检索数据 = 结果.get("data", {})
            检索结果数量 = len(检索数据.get("检索结果", []))
            路由日志器.info(
                f"管理员 {管理员['id']} 测试智能体检索成功: 找到 {检索结果数量} 个结果"
            )
            return 统一响应模型.成功(检索数据, "智能体检索测试成功")
        else:
            return 统一响应模型.失败(1539, 结果.get("error", "智能体检索测试失败"))

    except Exception as e:
        错误日志器.error(
            f"测试智能体检索API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1539, f"智能体检索测试失败: {str(e)}")


# ==================== 使用统计接口 ====================


@管理LangChain智能体路由.post("/statistics/usage", summary="获取智能体使用统计")
async def 获取智能体使用统计(
    请求数据: 使用统计查询请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取智能体使用统计数据

    - **开始日期**: 统计开始日期
    - **结束日期**: 统计结束日期
    - **智能体id**: 指定智能体（可选）
    - **用户id**: 指定用户（可选）
    - **统计维度**: 日/周/月统计
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取智能体使用统计")

        # 构建查询参数
        查询参数 = {
            "开始日期": 请求数据.开始日期,
            "结束日期": 请求数据.结束日期,
            "智能体id": 请求数据.智能体id,
            "用户id": 请求数据.用户id,
            "统计维度": 请求数据.统计维度,
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
        }

        # 调用服务层获取统计数据
        结果 = await LangChain智能体服务实例.获取使用统计数据(查询参数)

        路由日志器.info(f"管理员 {管理员['id']} 获取智能体使用统计成功")

        return 统一响应模型.成功(结果, "获取使用统计成功")

    except Exception as e:
        错误日志器.error(
            f"获取智能体使用统计API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1540, f"获取使用统计失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/statistics/conversation-records", summary="获取对话记录列表"
)
async def 获取对话记录列表(
    请求数据: 使用统计查询请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    获取对话记录详细列表

    - **开始日期**: 查询开始日期
    - **结束日期**: 查询结束日期
    - **智能体id**: 指定智能体（可选）
    - **用户id**: 指定用户（可选）
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取对话记录列表")

        # 构建查询参数
        查询参数 = {
            "开始日期": 请求数据.开始日期,
            "结束日期": 请求数据.结束日期,
            "智能体id": 请求数据.智能体id,
            "用户id": 请求数据.用户id,
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
        }

        # 调用服务层获取对话记录
        结果 = await LangChain智能体服务实例.获取对话记录列表(查询参数)

        路由日志器.info(
            f"管理员 {管理员['id']} 获取对话记录列表成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取对话记录成功")

    except Exception as e:
        错误日志器.error(
            f"获取对话记录列表API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1541, f"获取对话记录失败: {str(e)}")


@管理LangChain智能体路由.get("/statistics/dashboard", summary="获取统计仪表盘数据")
async def 获取统计仪表盘数据(管理员: dict = Depends(获取当前管理员用户)):
    """
    获取统计仪表盘数据
    包括：总体统计、趋势图表、热门智能体等
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取统计仪表盘数据")

        # 调用服务层获取仪表盘数据
        结果 = await LangChain智能体服务实例.获取统计仪表盘数据()

        路由日志器.info(f"管理员 {管理员['id']} 获取统计仪表盘数据成功")

        return 统一响应模型.成功(结果, "获取仪表盘数据成功")

    except Exception as e:
        错误日志器.error(
            f"获取统计仪表盘数据API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1542, f"获取仪表盘数据失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/users/verify-agent-permission", summary="验证用户智能体权限"
)
async def 验证用户智能体权限(
    请求数据: 用户权限验证请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    管理员验证用户是否有权限访问指定智能体

    用于编辑界面的用户API测试功能
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求验证用户权限: 用户{请求数据.用户id} -> 智能体{请求数据.智能体id}"
        )

        # 调用服务层验证用户权限
        验证结果 = await LangChain智能体服务实例.验证用户智能体访问权限(
            请求数据.用户id, 请求数据.智能体id
        )

        if 验证结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 用户权限验证成功: 用户{请求数据.用户id}有权限访问智能体{请求数据.智能体id}"
            )
            return 统一响应模型.成功(
                {
                    "有权限": True,
                    "访问类型": 验证结果.get("访问类型", "未知"),
                    "智能体信息": {
                        "智能体id": 请求数据.智能体id,
                        "智能体名称": 验证结果.get("智能体信息", {}).get(
                            "智能体名称", "未知"
                        ),
                        "状态": 验证结果.get("智能体信息", {}).get("状态", "未知"),
                    },
                },
                "用户有权限访问该智能体",
            )
        else:
            路由日志器.warning(
                f"管理员 {管理员['id']} 用户权限验证失败: 用户{请求数据.用户id}无权限访问智能体{请求数据.智能体id}"
            )
            return 统一响应模型.成功(
                {"有权限": False, "错误原因": 验证结果.get("error", "权限验证失败")},
                "用户无权限访问该智能体",
            )

    except Exception as e:
        错误日志器.error(
            f"验证用户智能体权限API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1541, f"验证用户智能体权限失败: {str(e)}")





# ==================== 模型供应商管理接口 ====================

# 注意：模型列表接口已迁移到专门的 管理_LangChain模型路由.py 文件中，避免重复接口
# 前端应该调用 /admin/langchain/model-providers/models/list 获取模型列表


@管理LangChain智能体路由.post(
    "/model-providers/models/{model_id}/detail", summary="获取模型详情"
)
async def 获取模型详情(
    model_id: int = Path(..., description="模型数据库ID"),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    获取指定模型的详细信息（通过数据库ID）
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取模型详情: ID {model_id}")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 获取模型详情（管理端专用方法）
        模型详情 = await LangChain模型管理器实例.获取模型详情_管理端(model_id)

        if not 模型详情:
            return 统一响应模型.失败(1404, f"模型不存在: ID {model_id}")

        路由日志器.info(f"管理员 {管理员['id']} 获取模型详情成功: ID {model_id}")

        return 统一响应模型.成功(模型详情, "获取模型详情成功")

    except Exception as e:
        错误日志器.error(
            f"获取模型详情API异常 (管理员: {管理员['id']}, 模型id: {model_id}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1534, f"获取模型详情失败: {str(e)}")


@管理LangChain智能体路由.post("/model-providers/refresh", summary="刷新模型配置")
async def 刷新模型配置(管理员: dict = Depends(获取当前管理员用户)):
    """
    刷新模型配置（重新从数据库加载）
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求刷新模型配置")

        # 刷新模型配置
        刷新结果 = await LangChain模型管理器实例.刷新模型配置()

        if 刷新结果:
            路由日志器.info(f"管理员 {管理员['id']} 刷新模型配置成功")
            return 统一响应模型.成功(
                {
                    "刷新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "模型数量": len(LangChain模型管理器实例.模型配置),
                },
                "模型配置刷新成功",
            )
        else:
            return 统一响应模型.失败(1535, "模型配置刷新失败")

    except Exception as e:
        错误日志器.error(
            f"刷新模型配置API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1535, f"刷新模型配置失败: {str(e)}")


@管理LangChain智能体路由.post("/model-providers/default", summary="设置默认模型")
async def 设置默认模型(
    请求数据: 设置默认模型请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    设置默认模型
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求设置默认模型: {请求数据.模型名称}")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 使用模型管理器的管理端方法设置默认模型
        设置结果 = await LangChain模型管理器实例.设置默认模型_管理端(请求数据.模型名称)

        if 设置结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 设置默认模型成功: {请求数据.模型名称}"
            )
            return 统一响应模型.成功(设置结果, "默认模型设置成功")
        else:
            return 统一响应模型.失败(1537, 设置结果.get("error", "设置默认模型失败"))

    except Exception as e:
        错误日志器.error(
            f"设置默认模型API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1537, f"设置默认模型失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/model-providers/config/update", summary="更新阿里云配置"
)
async def 更新阿里云配置(
    请求数据: dict = Body(...), 管理员: dict = Depends(获取当前管理员用户)
):
    """
    更新阿里云API配置
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求更新阿里云配置")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 更新配置
        if "api_key" in 请求数据:
            LangChain模型管理器实例.阿里云配置["api_key"] = 请求数据["api_key"]

        if "base_url" in 请求数据:
            LangChain模型管理器实例.阿里云配置["base_url"] = 请求数据["base_url"]

        # 重新初始化模型管理器
        await LangChain模型管理器实例.初始化(请求数据.get("api_key"))

        路由日志器.info(f"管理员 {管理员['id']} 更新阿里云配置成功")

        return 统一响应模型.成功(
            {
                "配置状态": "已更新",
                "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "API密钥状态": "已配置" if 请求数据.get("api_key") else "未配置",
            },
            "阿里云配置更新成功",
        )

    except Exception as e:
        错误日志器.error(
            f"更新阿里云配置API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1538, f"更新阿里云配置失败: {str(e)}")


# 注意：模型配置创建接口已迁移到专门的 管理_LangChain模型路由.py 文件中，避免重复接口


@管理LangChain智能体路由.post(
    "/model-providers/models/{model_id}/update", summary="更新模型配置"
)
async def 更新模型配置(
    model_id: int = Path(..., description="模型id"),
    请求数据: 更新模型配置请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    更新指定模型的配置信息
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求更新模型配置: ID {model_id}")

        # 初始化模型管理器
        from 服务.LangChain_模型管理器 import LangChain模型管理器实例

        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 构建更新数据
        更新数据 = {}

        if 请求数据.模型名称 is not None:
            更新数据["模型名称"] = 请求数据.模型名称
        if 请求数据.供应商名称 is not None:
            更新数据["提供商"] = 请求数据.供应商名称
        if 请求数据.模型功能类型 is not None:
            更新数据["模型类型"] = 请求数据.模型功能类型
        if 请求数据.API密钥 is not None:
            更新数据["API密钥"] = 请求数据.API密钥
        if 请求数据.API基础URL is not None:
            更新数据["API基础URL"] = 请求数据.API基础URL
        if 请求数据.模型参数 is not None:
            更新数据["模型参数"] = 请求数据.模型参数
        if 请求数据.是否启用 is not None:
            更新数据["启用状态"] = 1 if 请求数据.是否启用 else 0

        # 调用模型管理器更新模型配置
        结果 = await LangChain模型管理器实例.更新模型配置(model_id, 更新数据)

        if 结果.get("status") == 100:
            路由日志器.info(f"管理员 {管理员['id']} 更新模型配置成功: ID {model_id}")
            return 统一响应模型.成功(结果.get("data"), "模型配置更新成功")
        else:
            return 统一响应模型.失败(1541, 结果.get("message", "更新模型配置失败"))

    except Exception as e:
        错误日志器.error(
            f"更新模型配置API异常 (管理员: {管理员['id']}, 模型id: {model_id}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1541, f"更新模型配置失败: {str(e)}")


@管理LangChain智能体路由.post(
    "/model-providers/models/{model_id}/delete", summary="删除模型配置"
)
async def 删除模型配置(
    model_id: int = Path(..., description="模型id"),
    管理员: dict = Depends(获取当前管理员用户),
):
    """
    删除指定的模型配置
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求删除模型配置: ID {model_id}")

        # 初始化模型管理器
        from 服务.LangChain_模型管理器 import LangChain模型管理器实例

        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 调用模型管理器删除模型配置
        结果 = await LangChain模型管理器实例.删除模型配置(model_id)

        if 结果.get("status") == 100:
            路由日志器.info(f"管理员 {管理员['id']} 删除模型配置成功: ID {model_id}")
            return 统一响应模型.成功(结果.get("data"), "模型配置删除成功")
        else:
            return 统一响应模型.失败(1542, 结果.get("message", "删除模型配置失败"))

    except Exception as e:
        错误日志器.error(
            f"删除模型配置API异常 (管理员: {管理员['id']}, 模型id: {model_id}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1542, f"删除模型配置失败: {str(e)}")


class 测试向量模型请求模型(BaseModel):
    """测试向量模型请求模型"""

    模型id: int = Field(..., description="模型id")
    测试文本列表: List[str] = Field(
        ..., description="测试文本列表", min_length=2, max_length=10
    )

    @field_validator("测试文本列表")
    @classmethod
    def 验证测试文本(cls, v):
        for 文本 in v:
            if not 文本.strip():
                raise ValueError("测试文本不能为空")
            if len(文本) > 1000:
                raise ValueError("单个测试文本长度不能超过1000字符")
        return v


@管理LangChain智能体路由.get(
    "/model-providers/embedding-models", summary="获取向量模型列表"
)
async def 获取向量模型列表(管理员: dict = Depends(获取当前管理员用户)):
    """
    获取所有向量模型列表
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取向量模型列表")

        # 调用服务层获取向量模型列表
        结果 = await LangChain智能体服务实例.获取向量模型列表带统计()

        if 结果.get("success"):
            路由日志器.info(
                f"管理员 {管理员['id']} 获取向量模型列表成功，数量: {结果.get('总数', 0)}"
            )
            # 直接返回data字段的内容，而不是整个结果对象
            return 统一响应模型.成功(结果.get("data"), "获取向量模型列表成功")
        else:
            return 统一响应模型.失败(1548, 结果.get("error", "获取向量模型列表失败"))

    except Exception as e:
        错误日志器.error(
            f"获取向量模型列表API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1548, f"获取向量模型列表失败: {str(e)}")


# ==================== 新增请求模型 ====================


class 获取知识库详情请求模型(BaseModel):
    """获取知识库详情请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)


class 删除知识库请求模型(BaseModel):
    """删除知识库请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)
    确认删除: bool = Field(True, description="确认删除标志")


class 获取向量化状态请求模型(BaseModel):
    """获取向量化状态请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)


class 获取文档详情请求模型(BaseModel):
    """获取文档详情请求模型"""

    文档id: Union[int, str] = Field(..., description="文档id（支持数字ID或UUID字符串）")


class 文档预览请求模型(BaseModel):
    """文档预览请求模型"""

    文档id: Union[int, str] = Field(..., description="文档id（支持数字ID或UUID字符串）")
    最大长度: Optional[int] = Field(5000, description="预览最大长度", ge=100, le=50000)
    包含元数据: bool = Field(True, description="是否包含元数据")


class 文档下载请求模型(BaseModel):
    """文档下载请求模型"""

    文档id: Union[int, str] = Field(..., description="文档id（支持数字ID或UUID字符串）")


class 获取检索配置请求模型(BaseModel):
    """获取检索配置请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)


class 获取支持格式请求模型(BaseModel):
    """获取支持格式请求模型"""

    包含详情: bool = Field(True, description="是否包含详细信息")


class 获取向量模型列表请求模型(BaseModel):
    """获取向量模型列表请求模型"""

    模型类型: Optional[str] = Field("embedding", description="模型类型过滤")
    是否启用: Optional[bool] = Field(None, description="是否启用过滤")


class LangChain手动向量化文档请求(BaseModel):
    """手动向量化文档请求模型"""

    文档记录ID: int = Field(..., description="文档记录ID", ge=1)


class LangChain测试检索请求(BaseModel):
    """测试检索请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)
    查询文本: str = Field(..., description="查询文本", min_length=1, max_length=500)
    检索参数: Optional[Dict[str, Any]] = Field(default=None, description="检索参数")


# ==================== 知识库管理接口（改为POST） ====================


@管理LangChain智能体路由.post(
    "/langchain/manual-vectorize-document", summary="手动向量化文档"
)
async def 手动向量化文档(
    请求: LangChain手动向量化文档请求, 管理员: dict = Depends(获取当前管理员用户)
):
    """手动向量化文档"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求手动向量化文档: {请求.文档记录ID}")

        # 调用知识库服务层
        结果 = await LangChain知识库服务实例.手动向量化文档(请求.文档记录ID)

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 手动向量化文档成功: {请求.文档记录ID}"
            )
            return 统一响应模型.成功(结果)
        else:
            路由日志器.warning(
                f"管理员 {管理员['id']} 手动向量化文档失败: {结果.get('error', '未知错误')}"
            )
            return 统一响应模型.失败(1003, 结果.get("error", "手动向量化文档失败"))

    except Exception as e:
        路由日志器.error(f"手动向量化文档API异常 (管理员: {管理员['id']}): {str(e)}")
        return 统一响应模型.失败(500, f"手动向量化文档失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/detail", summary="获取知识库详情")
async def 获取知识库详情(
    请求数据: 获取知识库详情请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """获取知识库详情"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取知识库详情: {请求数据.知识id}")

        # 调用知识库服务层获取详情
        结果 = await LangChain知识库服务实例.获取知识库详情(请求数据.知识id)

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 获取知识库详情成功: {请求数据.知识id}"
            )
            return 统一响应模型.成功(结果["data"], "获取知识库详情成功")
        else:
            return 统一响应模型.失败(1527, 结果.get("error", "获取知识库详情失败"))

    except Exception as e:
        错误日志器.error(
            f"获取知识库详情API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1527, f"获取知识库详情失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/update", summary="更新知识库")
async def 更新知识库(
    请求数据: LangChain知识库更新请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """更新知识库"""
    try:
        知识id = 请求数据.知识id if hasattr(请求数据, "知识id") else None
        if not 知识id:
            return 统一响应模型.失败(1528, "知识id不能为空")

        路由日志器.info(f"管理员 {管理员['id']} 请求更新知识库: {知识id}")

        # 构建更新数据
        更新数据 = {}
        if 请求数据.知识库名称 is not None:
            更新数据["知识库名称"] = 请求数据.知识库名称
        if 请求数据.知识库描述 is not None:
            更新数据["知识库描述"] = 请求数据.知识库描述
        if 请求数据.状态 is not None:
            # 注意：数据库表中字段名是"知识库状态"，不是"状态"
            更新数据["知识库状态"] = 请求数据.状态
        if 请求数据.向量维度 is not None:
            更新数据["向量维度"] = 请求数据.向量维度
        if 请求数据.嵌入模型 is not None:
            更新数据["嵌入模型"] = 请求数据.嵌入模型
        # 注意：配置信息字段在当前数据库表中不存在，暂时忽略
        if 请求数据.配置信息 is not None:
            路由日志器.info(
                f"收到配置信息字段，但数据库表中暂无此字段，已忽略: {len(str(请求数据.配置信息))} 字符"
            )

        # 调用知识库服务层更新
        结果 = await LangChain知识库服务实例.更新知识库(知识id, 更新数据)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 更新知识库成功: {知识id}")
            return 统一响应模型.成功(结果, "更新知识库成功")
        else:
            return 统一响应模型.失败(1528, 结果.get("error", "更新知识库失败"))

    except Exception as e:
        错误日志器.error(
            f"更新知识库API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1528, f"更新知识库失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/delete", summary="删除知识库")
async def 删除知识库(
    请求数据: 删除知识库请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """删除知识库"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求删除知识库: {请求数据.知识id}")

        # 调用知识库服务层删除
        结果 = await LangChain知识库服务实例.删除知识库(请求数据.知识id)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 删除知识库成功: {请求数据.知识id}")
            return 统一响应模型.成功(结果, "删除知识库成功")
        else:
            return 统一响应模型.失败(1529, 结果.get("error", "删除知识库失败"))

    except Exception as e:
        错误日志器.error(
            f"删除知识库API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1529, f"删除知识库失败: {str(e)}")


# ==================== 文档管理接口（改为POST） ====================


@管理LangChain智能体路由.post("/documents/preview", summary="预览文档")
async def 预览文档(
    请求数据: 文档预览请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """预览文档"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求预览文档: {请求数据.文档id}")

        # 调用知识库服务层预览文档（智能识别ID类型）
        try:
            文档记录ID = int(请求数据.文档id)
            结果 = await LangChain知识库服务实例.通过ID预览文档(
                文档记录ID,
                {"最大长度": 请求数据.最大长度, "包含元数据": 请求数据.包含元数据},
            )
        except (ValueError, TypeError):
            # 如果转换失败，可能是UUID，尝试通过UUID预览
            结果 = await LangChain知识库服务实例.通过UUID预览文档(
                请求数据.文档id,
                {"最大长度": 请求数据.最大长度, "包含元数据": 请求数据.包含元数据},
            )

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 预览文档成功: {请求数据.文档id}")
            return 统一响应模型.成功(结果["data"], "预览文档成功")
        else:
            return 统一响应模型.失败(1535, 结果.get("error", "预览文档失败"))

    except Exception as e:
        错误日志器.error(
            f"预览文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1535, f"预览文档失败: {str(e)}")


@管理LangChain智能体路由.post("/documents/download", summary="下载文档")
async def 下载文档(
    请求数据: 文档下载请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """下载文档"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求下载文档: {请求数据.文档id}")

        # 调用知识库服务层下载文档（智能识别ID类型）
        try:
            文档记录ID = int(请求数据.文档id)
            结果 = await LangChain知识库服务实例.通过ID下载文档(文档记录ID)
        except (ValueError, TypeError):
            # 如果转换失败，可能是UUID，尝试通过UUID下载
            结果 = await LangChain知识库服务实例.通过UUID下载文档(请求数据.文档id)

        if 结果["success"]:
            路由日志器.info(f"管理员 {管理员['id']} 下载文档成功: {请求数据.文档id}")
            return 统一响应模型.成功(结果["data"], "下载文档成功")
        else:
            return 统一响应模型.失败(1536, 结果.get("error", "下载文档失败"))

    except Exception as e:
        错误日志器.error(
            f"下载文档API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1536, f"下载文档失败: {str(e)}")


# ==================== 结构化输出相关接口 ====================


class Pydantic模型验证请求模型(BaseModel):
    """Pydantic模型验证请求模型"""

    模型定义: str = Field(..., description="Pydantic模型定义代码", min_length=1)
    模式: str = Field(
        default="with_structured_output",
        description="输出模式，固定为with_structured_output",
    )


@管理LangChain智能体路由.post(
    "/validate-pydantic-model", summary="验证Pydantic模型定义"
)
async def 验证Pydantic模型定义(
    请求数据: Pydantic模型验证请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    验证Pydantic模型定义是否正确 - 基于Context7最佳实践
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求验证Pydantic模型定义")

        # 确保结构化输出处理器已初始化
        if not LangChain结构化输出处理器实例.已初始化:
            await LangChain结构化输出处理器实例.初始化()

        # 调用结构化输出处理器验证模型
        验证结果 = await LangChain结构化输出处理器实例.验证Pydantic模型定义(
            请求数据.模型定义
        )

        if 验证结果.get("valid"):
            路由日志器.info(f"管理员 {管理员['id']} Pydantic模型验证成功")
            return 统一响应模型.成功(验证结果, "Pydantic模型验证成功")
        else:
            return 统一响应模型.失败(
                1541, 验证结果.get("error", "Pydantic模型验证失败")
            )

    except Exception as e:
        错误日志器.error(
            f"验证Pydantic模型API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1542, f"验证Pydantic模型失败: {str(e)}")


# Pydantic预设模板API已移除，使用可视化JSON设计器

# 模板API代码已全部移除


class 测试智能体结构化输出请求模型(BaseModel):
    """测试智能体结构化输出请求模型 - 简化版本，只支持JSON Schema"""

    智能体id: int = Field(..., description="智能体id", ge=1)
    测试消息: str = Field(..., description="测试消息", min_length=1, max_length=1000)
    # 只支持JSON Schema方式
    json_schema: Optional[Dict[str, Any]] = Field(
        default=None, description="JSON Schema配置"
    )
    输出格式: str = Field(default="pydantic", description="输出格式")


@管理LangChain智能体路由.post(
    "/test-agent-structured-output", summary="测试智能体结构化输出"
)
async def 测试智能体结构化输出(
    请求数据: 测试智能体结构化输出请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """
    测试智能体结构化输出功能 - 基于Context7最佳实践
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求测试智能体结构化输出: 智能体id {请求数据.智能体id}"
        )

        # 服务层已使用简化初始化模式

        # 简化验证逻辑：只支持JSON Schema
        json_schema_to_use = None

        if 请求数据.json_schema:
            # 使用前端传递的JSON Schema
            json_schema_to_use = 请求数据.json_schema

            # 验证JSON Schema格式
            验证结果 = (
                await LangChain结构化输出处理器实例.从JSON_Schema创建Pydantic模型(
                    json_schema_to_use
                )
            )
            if not 验证结果.get("success"):
                return 统一响应模型.失败(
                    1548, f"JSON Schema验证失败: {验证结果.get('error', '未知错误')}"
                )

        else:
            # 从智能体配置中获取JSON Schema
            智能体详情 = await LangChain智能体数据层实例.获取智能体详情完整(
                请求数据.智能体id
            )
            if not 智能体详情:
                return 统一响应模型.失败(1546, "智能体不存在")

            json_schema_to_use = 智能体详情.get("json_schema")
            if not json_schema_to_use:
                return 统一响应模型.失败(1547, "智能体未配置结构化输出")

            # 从JSON Schema创建Pydantic模型进行验证
            验证结果 = (
                await LangChain结构化输出处理器实例.从JSON_Schema创建Pydantic模型(
                    json_schema_to_use
                )
            )
            if not 验证结果.get("success"):
                return 统一响应模型.失败(
                    1549, f"JSON Schema验证失败: {验证结果.get('error', '未知错误')}"
                )

        # 获取智能体实例
        智能体实例 = await LangChain智能体服务实例.获取智能体实例(请求数据.智能体id)
        if not 智能体实例:
            return 统一响应模型.失败(1550, "智能体不存在或未运行")

        # 创建模型类 - 简化版本，只支持JSON Schema
        创建结果 = await LangChain结构化输出处理器实例.从JSON_Schema创建Pydantic模型(
            json_schema_to_use
        )

        if not 创建结果["success"]:
            return 统一响应模型.失败(1551, f"创建Pydantic模型失败: {创建结果['error']}")

        模型类 = 创建结果["模型类"]

        # 执行实际的结构化输出测试 - Context7最佳实践
        try:
            # 调用智能体进行结构化输出测试
            对话结果 = await LangChain智能体服务实例.处理对话(
                智能体id=请求数据.智能体id,
                用户消息=请求数据.测试消息,
                用户id=管理员["id"],
                会话ID=f"test_structured_{请求数据.智能体id}_{管理员['id']}",
                结构化输出模式=True,
                pydantic_模型类=模型类,
            )

            if 对话结果.get("success"):
                测试结果 = {
                    "测试状态": "成功",
                    "模型名称": 模型类.__name__,
                    "字段数量": len(创建结果.get("字段信息", [])),
                    "AI回复": 对话结果.get("response", ""),
                    "结构化数据": 对话结果.get("structured_data"),
                    "处理时间": 对话结果.get("processing_time", 0),
                    "示例输出": 创建结果.get("示例数据", {}),
                    "JSON格式": 创建结果.get("JSON输出", ""),
                    "测试消息": 请求数据.测试消息,
                    "验证信息": "结构化输出测试成功",
                }
            else:
                测试结果 = {
                    "测试状态": "失败",
                    "模型名称": 模型类.__name__,
                    "字段数量": len(创建结果.get("字段信息", [])),
                    "错误信息": 对话结果.get("error", "智能体调用失败"),
                    "示例输出": 创建结果.get("示例数据", {}),
                    "测试消息": 请求数据.测试消息,
                    "验证信息": "模型创建成功，但智能体调用失败",
                }

        except Exception as e:
            # 如果实际调用失败，返回模拟结果
            路由日志器.warning(f"智能体调用失败，返回模拟结果: {str(e)}")
            测试结果 = {
                "测试状态": "模拟成功",
                "模型名称": 模型类.__name__,
                "字段数量": len(创建结果.get("字段信息", [])),
                "示例输出": 创建结果.get("示例数据", {}),
                "JSON格式": 创建结果.get("JSON输出", ""),
                "测试消息": 请求数据.测试消息,
                "智能体响应": f"基于模型 {模型类.__name__} 的结构化响应示例（模拟）",
                "验证信息": "Pydantic模型验证通过，可以正常使用结构化输出",
                "注意": "这是模拟结果，实际调用可能需要智能体正常运行",
            }

        路由日志器.info(f"管理员 {管理员['id']} 测试智能体结构化输出成功")
        return 统一响应模型.成功(测试结果, "智能体结构化输出测试成功")

    except Exception as e:
        错误日志器.error(
            f"测试智能体结构化输出API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1548, f"测试智能体结构化输出失败: {str(e)}")


# ==================== PostgreSQL向量存储管理接口 ====================


class 向量存储状态请求模型(BaseModel):
    """向量存储状态请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)


class 清空向量数据请求模型(BaseModel):
    """清空向量数据请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)


@管理LangChain智能体路由.post("/knowledge/vector-status", summary="获取向量存储状态")
async def 获取向量存储状态(
    请求数据: 向量存储状态请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """获取知识库PostgreSQL向量存储状态"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求获取向量存储状态: {请求数据.知识id}"
        )

        状态结果 = await LangChain知识库服务实例.获取向量存储状态(请求数据.知识id)

        if 状态结果.get("success"):
            路由日志器.info(f"管理员 {管理员['id']} 获取向量存储状态成功")
            return 统一响应模型.成功(状态结果.get("data"), "获取向量存储状态成功")
        else:
            return 统一响应模型.失败(
                1550, 状态结果.get("error", "获取向量存储状态失败")
            )

    except Exception as e:
        错误日志器.error(
            f"获取向量存储状态API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1550, f"获取向量存储状态失败: {str(e)}")


@管理LangChain智能体路由.post("/knowledge/clear-vectors", summary="清空知识库向量数据")
async def 清空知识库向量数据(
    请求数据: 清空向量数据请求模型, 管理员: dict = Depends(获取当前管理员用户)
):
    """清空知识库向量数据"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求清空知识库向量数据: {请求数据.知识id}"
        )

        清空结果 = await LangChain知识库服务实例.清空知识库向量数据(请求数据.知识id)

        if 清空结果.get("success"):
            路由日志器.info(f"管理员 {管理员['id']} 清空知识库向量数据成功")
            return 统一响应模型.成功(
                {"知识id": 请求数据.知识id}, "知识库向量数据已清空"
            )
        else:
            return 统一响应模型.失败(
                1551, 清空结果.get("error", "清空知识库向量数据失败")
            )

    except Exception as e:
        错误日志器.error(
            f"清空知识库向量数据API异常 (管理员: {管理员['id']}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1551, f"清空知识库向量数据失败: {str(e)}")


# ==================== 查询优化配置管理接口 ====================


class 查询优化配置请求模型(BaseModel):
    """查询优化配置请求模型"""

    智能体id: int = Field(..., description="智能体id", ge=1)
    知识id: int = Field(..., description="知识id", ge=1)
    启用查询优化: bool = Field(False, description="是否启用查询优化")
    查询优化策略: str = Field(
        "rewrite", description="查询优化策略", pattern="^(rewrite|expand|multi_query)$"
    )
    查询优化模型id: Optional[int] = Field(None, description="查询优化模型id")
    查询优化提示词: Optional[str] = Field(
        None, description="查询优化提示词模板", max_length=2000
    )


@管理LangChain智能体路由.get(
    "/agents/{agent_id}/query-optimization", summary="获取智能体查询优化配置"
)
async def 获取智能体查询优化配置(
    agent_id: int = Path(..., description="智能体id"),
    管理员: dict = Depends(获取当前管理员用户),
):
    """获取智能体的查询优化配置"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取智能体查询优化配置: {agent_id}")

        # 获取智能体关联知识库配置
        from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例

        知识库配置列表 = await LangChain智能体数据层实例.获取智能体关联知识库配置(
            agent_id
        )

        if not 知识库配置列表:
            return 统一响应模型.失败(1404, "智能体未关联任何知识库")

        # 格式化返回数据
        配置列表 = []
        for 配置 in 知识库配置列表:
            配置列表.append(
                {
                    "关联id": 配置.get("关联id"),
                    "知识id": 配置.get("知识id"),
                    "知识库名称": 配置.get("知识库名称"),
                    "查询优化配置": 配置.get("查询优化配置", {}),
                    "检索配置": {
                        "检索策略": 配置.get("检索策略"),
                        "最大检索数量": 配置.get("最大检索数量"),
                        "相似度阈值": 配置.get("相似度阈值"),
                    },
                }
            )

        路由日志器.info(
            f"管理员 {管理员['id']} 获取智能体查询优化配置成功: {len(配置列表)} 个知识库"
        )
        return 统一响应模型.成功(配置列表, "获取查询优化配置成功")

    except Exception as e:
        错误日志器.error(f"获取智能体查询优化配置API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1553, f"获取查询优化配置失败: {str(e)}")


@管理LangChain智能体路由.put(
    "/agents/query-optimization", summary="更新智能体查询优化配置"
)
async def 更新智能体查询优化配置(
    请求数据: 查询优化配置请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """更新智能体的查询优化配置"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求更新查询优化配置: 智能体{请求数据.智能体id}, 知识库{请求数据.知识id}"
        )

        # 更新关联表中的查询优化配置
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        更新SQL = """
        UPDATE langchain_智能体知识库关联表
        SET
            启用查询优化 = $1,
            查询优化策略 = $1,
            查询优化模型id = $1,
            查询优化提示词 = $1
        WHERE langchain_智能体配置表id = $1 AND langchain_知识库表id = $2
        """

        参数 = (
            请求数据.启用查询优化,
            请求数据.查询优化策略,
            请求数据.查询优化模型id,
            请求数据.查询优化提示词,
            请求数据.智能体id,
            请求数据.知识id,
        )

        影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数)

        if 影响行数 > 0:
            路由日志器.info(
                f"管理员 {管理员['id']} 更新查询优化配置成功: 智能体{请求数据.智能体id}, 知识库{请求数据.知识id}"
            )
            return 统一响应模型.成功(
                {
                    "智能体id": 请求数据.智能体id,
                    "知识id": 请求数据.知识id,
                    "更新时间": datetime.now().isoformat(),
                    "配置": {
                        "启用查询优化": 请求数据.启用查询优化,
                        "查询优化策略": 请求数据.查询优化策略,
                        "查询优化模型id": 请求数据.查询优化模型id,
                    },
                },
                "查询优化配置更新成功",
            )
        else:
            return 统一响应模型.失败(1404, "未找到对应的智能体知识库关联记录")

    except Exception as e:
        错误日志器.error(f"更新智能体查询优化配置API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1554, f"更新查询优化配置失败: {str(e)}")


@管理LangChain智能体路由.put(
    "/knowledge/{knowledge_id}/default-query-optimization",
    summary="更新知识库默认查询优化配置",
)
async def 更新知识库默认查询优化配置(
    knowledge_id: int = Path(..., description="知识id"),
    请求数据: dict = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """更新知识库的默认查询优化配置"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求更新知识库默认查询优化配置: {knowledge_id}"
        )

        # 更新知识库表中的默认查询优化配置
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        更新字段 = []
        参数列表 = []

        if "默认启用查询优化" in 请求数据:
            更新字段.append("默认启用查询优化 = $1")
            参数列表.append(请求数据["默认启用查询优化"])

        if "默认查询优化策略" in 请求数据:
            更新字段.append("默认查询优化策略 = $1")
            参数列表.append(请求数据["默认查询优化策略"])

        if "默认查询优化模型id" in 请求数据:
            更新字段.append("默认查询优化模型id = $1")
            参数列表.append(请求数据["默认查询优化模型id"])

        if "默认查询优化提示词" in 请求数据:
            更新字段.append("默认查询优化提示词 = $1")
            参数列表.append(请求数据["默认查询优化提示词"])

        if not 更新字段:
            return 统一响应模型.失败(1400, "没有提供要更新的字段")

        参数列表.append(knowledge_id)

        更新SQL = f"""
        UPDATE langchain_知识库表
        SET {", ".join(更新字段)}
        WHERE id = $1
        """

        影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

        if 影响行数 > 0:
            路由日志器.info(
                f"管理员 {管理员['id']} 更新知识库默认查询优化配置成功: {knowledge_id}"
            )
            return 统一响应模型.成功(
                {
                    "知识id": knowledge_id,
                    "更新时间": datetime.now().isoformat(),
                    "更新字段": list(请求数据.keys()),
                },
                "知识库默认查询优化配置更新成功",
            )
        else:
            return 统一响应模型.失败(1404, "未找到对应的知识库")

    except Exception as e:
        错误日志器.error(f"更新知识库默认查询优化配置API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1555, f"更新知识库默认查询优化配置失败: {str(e)}")


@管理LangChain智能体路由.get(
    "/query-optimization/models", summary="获取可用查询优化模型列表"
)
async def 获取可用查询优化模型列表(
    管理员: dict = Depends(获取当前管理员用户),
):
    """获取可用于查询优化的模型列表"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取可用查询优化模型列表")

        # 调用知识库服务获取模型列表
        结果 = await LangChain知识库服务实例.获取可用查询优化模型列表()

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 获取查询优化模型列表成功: {len(结果['data'])}个模型"
            )
            return 统一响应模型.成功(结果["data"], "获取查询优化模型列表成功")
        else:
            return 统一响应模型.失败(
                1556, 结果.get("error", "获取查询优化模型列表失败")
            )

    except Exception as e:
        错误日志器.error(f"获取可用查询优化模型列表API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1556, f"获取查询优化模型列表失败: {str(e)}")





@管理LangChain智能体路由.get(
    "/knowledge/{knowledge_id}/query-optimization", summary="获取知识库查询优化配置"
)
async def 获取知识库查询优化配置(
    knowledge_id: int = Path(..., description="知识id"),
    管理员: dict = Depends(获取当前管理员用户),
):
    """获取知识库的默认查询优化配置"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求获取知识库查询优化配置: {knowledge_id}"
        )

        # 调用知识库服务获取配置
        结果 = await LangChain知识库服务实例.获取知识库查询优化配置(knowledge_id)

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 获取知识库查询优化配置成功: {knowledge_id}"
            )
            return 统一响应模型.成功(结果["data"], "获取知识库查询优化配置成功")
        else:
            return 统一响应模型.失败(
                1558, 结果.get("error", "获取知识库查询优化配置失败")
            )

    except Exception as e:
        错误日志器.error(f"获取知识库查询优化配置API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1558, f"获取知识库查询优化配置失败: {str(e)}")


# ==================== 查询优化测试接口 ====================


class 查询优化测试请求模型(BaseModel):
    """查询优化测试请求模型"""

    知识id: int = Field(..., description="知识id", ge=1)
    原始查询: str = Field(
        ..., description="原始查询文本", min_length=1, max_length=1000
    )
    启用查询优化: bool = Field(True, description="是否启用查询优化")
    查询优化策略: str = Field(
        "rewrite", description="查询优化策略", pattern="^(rewrite|expand|multi_query)$"
    )
    查询优化模型id: Optional[int] = Field(None, description="查询优化模型id")
    查询优化提示词: Optional[str] = Field(None, description="查询优化提示词模板")


@管理LangChain智能体路由.post("/query-optimization/test", summary="测试查询优化效果")
async def 测试查询优化效果(
    请求数据: 查询优化测试请求模型 = Body(...),
    管理员: dict = Depends(获取当前管理员用户),
):
    """测试查询优化效果对比"""
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求测试查询优化效果: 知识库{请求数据.知识id}, 查询'{请求数据.原始查询[:50]}...'"
        )

        # 构建查询优化配置
        查询优化配置 = {
            "启用": 请求数据.启用查询优化,
            "优化策略": 请求数据.查询优化策略,
            "优化模型id": 请求数据.查询优化模型id,
            "提示词模板": 请求数据.查询优化提示词,
        }

        # 调用知识库服务测试优化效果
        结果 = await LangChain知识库服务实例.测试查询优化效果(
            请求数据.知识id, 请求数据.原始查询, 查询优化配置
        )

        if 结果["success"]:
            路由日志器.info(
                f"管理员 {管理员['id']} 查询优化效果测试成功: 知识库{请求数据.知识id}"
            )
            return 统一响应模型.成功(结果["data"], "查询优化效果测试成功")
        else:
            return 统一响应模型.失败(1559, 结果.get("error", "查询优化效果测试失败"))

    except Exception as e:
        错误日志器.error(f"测试查询优化效果API异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1559, f"查询优化效果测试失败: {str(e)}")
