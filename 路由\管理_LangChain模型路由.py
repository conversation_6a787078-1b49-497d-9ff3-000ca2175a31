"""
LangChain模型管理路由
专门处理模型配置、测试、管理等相关功能
"""

# 导入日志
import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field, validator

# 导入认证和响应模型
from 依赖项.认证 import 获取当前管理员用户 as 获取当前管理员
from 数据模型.响应模型 import 统一响应模型
from 服务.LangChain_模型管理器 import LangChain模型管理器实例

路由日志器 = logging.getLogger("管理.LangChain模型路由")

# 创建路由器（prefix在main.py中统一配置）
router = APIRouter(tags=["LangChain模型管理"])

# 导出路由器供main.py使用
管理LangChain模型路由 = router

# ==================== 请求模型定义 ====================


class 模型测试请求模型(BaseModel):
    """模型测试请求模型"""

    模型id: int = Field(..., description="模型id")
    测试消息: str = Field(..., description="测试消息", min_length=1, max_length=500)

    @validator("测试消息")
    def 验证测试消息(cls, v):
        if not v.strip():
            raise ValueError("测试消息不能为空")
        return v.strip()


class 向量模型测试请求模型(BaseModel):
    """向量模型测试请求模型"""

    模型id: int = Field(..., description="模型id")
    测试消息: str = Field(
        "你好", description="主要测试消息", min_length=1, max_length=500
    )
    测试文本列表: Optional[List[str]] = Field(
        None, description="可选的测试文本列表", min_length=1, max_length=10
    )

    @validator("测试消息")
    def 验证测试消息(cls, v):
        if not v.strip():
            raise ValueError("测试消息不能为空")
        return v.strip()

    @validator("测试文本列表")
    def 验证测试文本(cls, v):
        if v is not None:
            for 文本 in v:
                if not 文本.strip():
                    raise ValueError("测试文本不能为空")
            return [文本.strip() for 文本 in v]
        return v


class 模型配置请求模型(BaseModel):
    """模型配置请求模型"""

    模型名称: str = Field(..., description="模型名称")
    模型类型: str = Field(..., description="模型类型")
    显示名称: str = Field(..., description="显示名称")
    提供商: str = Field(..., description="提供商")
    API密钥: str = Field("", description="API密钥")
    API基础URL: str = Field("", description="API基础URL")
    模型参数: Dict[str, Any] = Field(default_factory=dict, description="模型参数")
    # 注意：langchain_模型配置表中没有启用状态字段


class 模型更新请求模型(BaseModel):
    """模型更新请求模型"""

    模型名称: Optional[str] = Field(None, description="模型名称")
    显示名称: Optional[str] = Field(None, description="显示名称")
    API密钥: Optional[str] = Field(None, description="API密钥")
    API基础URL: Optional[str] = Field(None, description="API基础URL")
    模型参数: Optional[Dict[str, Any]] = Field(None, description="模型参数")
    # 注意：langchain_模型配置表中没有启用状态字段


class 模型列表请求模型(BaseModel):
    """模型列表请求模型"""

    模型类型: Optional[str] = Field(None, description="模型类型筛选")
    # 注意：langchain_模型配置表中没有启用状态字段
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)


# ==================== 路由处理函数 ====================


@router.post(
    "/model-providers/test-chat",
    summary="测试文本生成模型",
    description="专门测试文本生成模型的对话能力",
)
async def 测试文本生成模型(
    请求数据: 模型测试请求模型, 管理员: dict = Depends(获取当前管理员)
):
    """
    文本生成模型测试接口
    - 专门测试对话生成模型
    - 返回对话测试结果
    """
    try:
        路由日志器.info(
            f"管理员 {管理员['id']} 请求测试文本生成模型: ID {请求数据.模型id}"
        )

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 获取模型信息
        from 服务.LangChain_智能体服务 import LangChain智能体服务实例

        模型信息 = await LangChain智能体服务实例.根据ID或名称获取模型信息(
            模型id=请求数据.模型id
        )

        if not 模型信息:
            return 统一响应模型.失败(1404, f"模型不存在或未启用: ID {请求数据.模型id}")

        模型名称 = 模型信息["模型名称"]

        # 执行文本生成模型测试
        路由日志器.info(f"执行文本生成模型测试: {模型名称}")

        测试结果 = await LangChain模型管理器实例.测试模型连接_管理端(
            {"模型名称": 模型名称, "测试消息": 请求数据.测试消息}
        )

        if 测试结果.get("success"):
            路由日志器.info(f"管理员 {管理员['id']} 文本生成模型测试成功: {模型名称}")
            return 统一响应模型.成功(测试结果, "文本生成模型测试成功")
        else:
            return 统一响应模型.失败(
                1502, 测试结果.get("error", "文本生成模型测试失败")
            )

    except Exception as e:
        路由日志器.error(
            f"测试文本生成模型API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1502, f"测试文本生成模型失败: {str(e)}")


@router.post(
    "/model-providers/test-embedding",
    summary="测试向量模型",
    description="专门测试向量模型的向量化能力",
)
async def 测试向量模型(
    请求数据: 向量模型测试请求模型, 管理员: dict = Depends(获取当前管理员)
):
    """
    向量模型测试接口
    - 专门测试向量模型
    - 支持多文本向量化测试
    - 返回向量化结果和相似度分析
    """
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求测试向量模型: ID {请求数据.模型id}")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 直接从数据库获取模型信息
        from 数据.LangChain_模型数据层 import LangChain模型数据层实例

        # 确保数据层已初始化
        if not LangChain模型数据层实例.已初始化:
            await LangChain模型数据层实例.初始化()

        模型信息 = await LangChain模型数据层实例.获取模型配置详情(请求数据.模型id)

        if not 模型信息:
            return 统一响应模型.失败(1404, f"模型不存在: ID {请求数据.模型id}")

        # 注意：langchain_模型配置表中没有启用状态字段，所有模型默认启用

        模型名称 = 模型信息["模型名称"]
        模型类型 = 模型信息.get("模型类型", "")
        提供商 = 模型信息.get("提供商", "未知")

        # 验证是否为向量模型
        if "embedding" not in 模型类型.lower():
            return 统一响应模型.失败(1400, f"模型不是向量模型: {模型类型}")

        # 执行向量模型测试
        路由日志器.info(f"执行数据库向量模型测试: {模型名称} (ID: {请求数据.模型id})")

        # 构建测试文本列表
        测试文本列表 = 请求数据.测试文本列表 or [
            请求数据.测试消息,
            "这是一个测试文本用于验证向量模型功能",
            "人工智能技术正在快速发展",
            "机器学习是AI的重要分支",
        ]

        测试结果 = await LangChain模型管理器实例.测试向量模型(
            {"模型名称": 模型名称, "测试文本列表": 测试文本列表}
        )

        if 测试结果.get("success"):
            路由日志器.info(f"管理员 {管理员['id']} 数据库向量模型测试成功: {模型名称}")

            # 增强测试结果信息
            增强结果 = {
                **测试结果,
                "模型信息": {
                    "数据库ID": 请求数据.模型id,
                    "模型名称": 模型名称,
                    "模型类型": 模型类型,
                    "提供商": 提供商,
                    "测试方式": "数据库真实配置",
                },
            }

            return 统一响应模型.成功(增强结果, "向量模型测试成功")
        else:
            路由日志器.error(
                f"管理员 {管理员['id']} 数据库向量模型测试失败: {模型名称} - {测试结果.get('error')}"
            )
            return 统一响应模型.失败(1503, 测试结果.get("error", "向量模型测试失败"))

    except Exception as e:
        路由日志器.error(
            f"测试向量模型API异常 (管理员: {管理员['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1503, f"测试向量模型失败: {str(e)}")


# ==================== 模型列表管理接口 ====================


@router.post(
    "/model-providers/models/list",
    summary="获取模型列表",
    description="获取模型配置列表，支持筛选和分页",
)
async def 获取模型列表(
    请求数据: 模型列表请求模型, 管理员: dict = Depends(获取当前管理员)
):
    """获取模型配置列表"""
    try:
        路由日志器.info(f"🔍 管理员 {管理员['id']} 请求获取模型列表: {请求数据}")
        路由日志器.info(
            f"📊 请求参数详情: 模型类型={请求数据.模型类型}, 页码={请求数据.页码}, 每页数量={请求数据.每页数量}"
        )

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 调用模型管理器获取列表
        if 请求数据.模型类型 and "embedding" in 请求数据.模型类型.lower():
            # 获取向量模型列表
            结果 = await LangChain模型管理器实例.获取向量模型列表带统计()
            if 结果.get("status") == 100:
                return 统一响应模型.成功(结果.get("data"), "获取向量模型列表成功")
            else:
                return 统一响应模型.失败(
                    1500, f"获取向量模型列表失败: {结果.get('message', '未知错误')}"
                )
        else:
            # 获取对话模型列表 - 直接从数据库获取
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            # 根据请求参数过滤模型

            # 处理模型类型过滤 - 将前端的chat类型映射到数据库的实际枚举值
            模型类型过滤 = None
            if 请求数据.模型类型:
                if 请求数据.模型类型 == "chat":
                    # 对话模型包括多种类型
                    模型类型过滤 = [
                        "openai_chat",
                        "openai_gpt",
                        "alibaba_qwen",
                        "anthropic_claude",
                        "google_palm",
                    ]
                elif 请求数据.模型类型 == "embedding":
                    # 嵌入模型类型
                    模型类型过滤 = [
                        "openai_embedding",
                        "alibaba_embedding",
                        "google_embedding",
                        "local_embedding",
                    ]
                else:
                    # 其他类型直接使用
                    模型类型过滤 = 请求数据.模型类型

            # 获取所有模型，然后在应用层过滤
            所有模型列表 = await LangChain模型数据层实例.获取模型列表(None)

            # 应用模型类型过滤
            if 模型类型过滤 and isinstance(模型类型过滤, list):
                模型列表 = [
                    模型
                    for 模型 in 所有模型列表
                    if 模型.get("模型类型") in 模型类型过滤
                ]
            elif 模型类型过滤:
                模型列表 = [
                    模型
                    for 模型 in 所有模型列表
                    if 模型.get("模型类型") == 模型类型过滤
                ]
            else:
                模型列表 = 所有模型列表

            # 如果数据库中没有模型，创建一些默认模型
            if not 模型列表:
                路由日志器.info("数据库中没有模型数据，创建默认模型...")
                # 注意：langchain_模型配置表中没有启用状态字段
                默认模型列表 = [
                    {
                        "模型名称": "qwen-turbo",
                        "模型类型": "alibaba_qwen",
                        "显示名称": "通义千问Turbo",
                        "提供商": "阿里云",
                        "API密钥": "",
                        "API基础URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                        "模型参数": {"temperature": 0.7, "max_tokens": 4000},
                        "最大令牌数": 8000,
                        "算力消耗": 1.0,
                    },
                    {
                        "模型名称": "qwen-plus",
                        "模型类型": "alibaba_qwen",
                        "显示名称": "通义千问Plus",
                        "提供商": "阿里云",
                        "API密钥": "",
                        "API基础URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                        "模型参数": {"temperature": 0.7, "max_tokens": 8000},
                        "最大令牌数": 32000,
                        "算力消耗": 2.0,
                    },
                    {
                        "模型名称": "qwen-max",
                        "模型类型": "alibaba_qwen",
                        "显示名称": "通义千问Max",
                        "提供商": "阿里云",
                        "API密钥": "",
                        "API基础URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                        "模型参数": {"temperature": 0.7, "max_tokens": 4000},
                        "最大令牌数": 8000,
                        "算力消耗": 4.0,
                    },
                    {
                        "模型名称": "gpt-3.5-turbo",
                        "模型类型": "openai_chat",
                        "显示名称": "GPT-3.5 Turbo",
                        "提供商": "OpenAI",
                        "API密钥": "",
                        "API基础URL": "https://api.openai.com/v1",
                        "模型参数": {"temperature": 0.7, "max_tokens": 4000},
                        "最大令牌数": 4000,
                        "算力消耗": 2.0,
                    },
                    {
                        "模型名称": "gpt-4",
                        "模型类型": "openai_gpt",
                        "显示名称": "GPT-4",
                        "提供商": "OpenAI",
                        "API密钥": "",
                        "API基础URL": "https://api.openai.com/v1",
                        "模型参数": {"temperature": 0.7, "max_tokens": 8000},
                        "最大令牌数": 8000,
                        "算力消耗": 5.0,
                    },
                ]

                # 创建默认模型
                for 模型数据 in 默认模型列表:
                    try:
                        await LangChain模型数据层实例.创建模型配置(模型数据)
                    except Exception as e:
                        路由日志器.warning(
                            f"创建默认模型失败: {模型数据['模型名称']} - {str(e)}"
                        )

                # 重新获取模型列表，应用相同的过滤条件
                所有模型列表 = await LangChain模型数据层实例.获取模型列表(None)

                # 重新应用模型类型过滤
                if 模型类型过滤 and isinstance(模型类型过滤, list):
                    模型列表 = [
                        模型
                        for 模型 in 所有模型列表
                        if 模型.get("模型类型") in 模型类型过滤
                    ]
                elif 模型类型过滤:
                    模型列表 = [
                        模型
                        for 模型 in 所有模型列表
                        if 模型.get("模型类型") == 模型类型过滤
                    ]
                else:
                    模型列表 = 所有模型列表

            # 应用分页
            页码 = 请求数据.页码 or 1
            每页数量 = 请求数据.每页数量 or 20
            总数量 = len(模型列表)

            开始索引 = (页码 - 1) * 每页数量
            结束索引 = 开始索引 + 每页数量
            分页模型列表 = 模型列表[开始索引:结束索引]

            结果 = {
                "列表": 分页模型列表,
                "总数": 总数量,
                "当前页": 页码,
                "每页数量": 每页数量,
                "总页数": (总数量 + 每页数量 - 1) // 每页数量,
            }

            路由日志器.info(
                f"获取模型列表成功，总数: {总数量}，当前页: {len(分页模型列表)}"
            )
            return 统一响应模型.成功(结果, "获取模型列表成功")

    except Exception as e:
        路由日志器.error(f"获取模型列表异常: {str(e)}")
        return 统一响应模型.失败(1500, f"获取模型列表异常: {str(e)}")


@router.post(
    "/model-providers/create", summary="创建模型配置", description="创建新的模型配置"
)
async def 创建模型配置(
    请求数据: 模型配置请求模型, 管理员: dict = Depends(获取当前管理员)
):
    """创建模型配置"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求创建模型配置: {请求数据.模型名称}")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 构建模型配置数据
        模型配置数据 = {
            "模型名称": 请求数据.模型名称,
            "模型类型": 请求数据.模型类型,
            "显示名称": 请求数据.显示名称,
            "提供商": 请求数据.提供商,
            "API密钥": 请求数据.API密钥,
            "API基础URL": 请求数据.API基础URL,
            "模型参数": 请求数据.模型参数,
            # 注意：langchain_模型配置表中没有启用状态字段
        }

        # 调用模型管理器创建配置
        结果 = await LangChain模型管理器实例.创建模型配置_管理端(模型配置数据)

        if 结果.get("status") == 100:
            路由日志器.info(
                f"管理员 {管理员['id']} 创建模型配置成功: {请求数据.模型名称}"
            )
            return 统一响应模型.成功(结果["message"], "模型配置创建成功")
        else:
            return 统一响应模型.失败(
                结果.get("status", 1500), 结果.get("message", "创建模型配置失败")
            )

    except Exception as e:
        路由日志器.error(f"创建模型配置异常: {str(e)}")
        return 统一响应模型.失败(1500, f"创建模型配置异常: {str(e)}")


@router.post(
    "/model-providers/models/{模型id}/update",
    summary="更新模型配置",
    description="更新指定模型的配置",
)
async def 更新模型配置(
    模型id: int, 请求数据: 模型更新请求模型, 管理员: dict = Depends(获取当前管理员)
):
    """更新模型配置"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求更新模型配置: ID {模型id}")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 构建更新数据（只包含非None的字段）
        # 注意：langchain_模型配置表中没有启用状态字段
        字段映射 = {
            "模型名称": 请求数据.模型名称,
            "显示名称": 请求数据.显示名称,
            "API密钥": 请求数据.API密钥,
            "API基础URL": 请求数据.API基础URL,
            "模型参数": 请求数据.模型参数,
        }
        更新数据 = {k: v for k, v in 字段映射.items() if v is not None}

        if not 更新数据:
            return 统一响应模型.失败(1400, "没有提供需要更新的数据")

        # 调用模型管理器更新配置
        结果 = await LangChain模型管理器实例.更新模型配置_管理端(模型id, 更新数据)

        if 结果.get("status") == 100:
            路由日志器.info(f"管理员 {管理员['id']} 更新模型配置成功: ID {模型id}")
            return 统一响应模型.成功(结果["message"], "模型配置更新成功")
        else:
            return 统一响应模型.失败(
                结果.get("status", 1500), 结果.get("message", "更新模型配置失败")
            )

    except Exception as e:
        路由日志器.error(f"更新模型配置异常: {str(e)}")
        return 统一响应模型.失败(1500, f"更新模型配置异常: {str(e)}")


@router.post(
    "/model-providers/models/{模型id}/delete",
    summary="删除模型配置",
    description="删除指定的模型配置",
)
async def 删除模型配置(模型id: int, 管理员: dict = Depends(获取当前管理员)):
    """删除模型配置"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求删除模型配置: ID {模型id}")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 调用模型管理器删除配置
        结果 = await LangChain模型管理器实例.删除模型配置_管理端(模型id)

        if 结果.get("status") == 100:
            路由日志器.info(f"管理员 {管理员['id']} 删除模型配置成功: ID {模型id}")
            return 统一响应模型.成功(结果["message"], "模型配置删除成功")
        else:
            return 统一响应模型.失败(
                结果.get("status", 1500), 结果.get("message", "删除模型配置失败")
            )

    except Exception as e:
        路由日志器.error(f"删除模型配置异常: {str(e)}")
        return 统一响应模型.失败(1500, f"删除模型配置异常: {str(e)}")


@router.post(
    "/model-providers/models/{模型id}/detail",
    summary="获取模型详情",
    description="获取指定模型的详细配置信息",
)
async def 获取模型详情(模型id: int, 管理员: dict = Depends(获取当前管理员)):
    """获取模型详情"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取模型详情: ID {模型id}")

        # 使用智能体服务获取模型信息
        from 服务.LangChain_智能体服务 import LangChain智能体服务实例

        模型信息 = await LangChain智能体服务实例.根据ID或名称获取模型信息(模型id=模型id)

        if 模型信息:
            路由日志器.info(f"管理员 {管理员['id']} 获取模型详情成功: ID {模型id}")
            return 统一响应模型.成功(模型信息, "获取模型详情成功")
        else:
            return 统一响应模型.失败(1404, f"模型不存在: ID {模型id}")

    except Exception as e:
        路由日志器.error(f"获取模型详情异常: {str(e)}")
        return 统一响应模型.失败(1500, f"获取模型详情异常: {str(e)}")


@router.post(
    "/model-providers/statistics",
    summary="获取模型统计概览",
    description="获取模型使用统计和概览信息",
)
async def 获取模型统计概览(管理员: dict = Depends(获取当前管理员)):
    """获取模型统计概览"""
    try:
        路由日志器.info(f"管理员 {管理员['id']} 请求获取模型统计概览")

        # 确保模型管理器已初始化
        if not LangChain模型管理器实例.已初始化:
            await LangChain模型管理器实例.初始化()

        # 获取所有模型统计
        所有模型结果 = await LangChain模型管理器实例.获取模型列表带统计()
        向量模型结果 = await LangChain模型管理器实例.获取向量模型列表带统计()

        if 所有模型结果.get("success") and 向量模型结果.get("success"):
            所有模型 = 所有模型结果.get("模型列表", [])
            向量模型 = 向量模型结果.get("模型列表", [])

            # 计算统计数据
            统计概览 = {
                "模型总数": len(所有模型),
                "向量模型数量": len(向量模型),
                "文本生成模型数量": len(所有模型) - len(向量模型),
                # 注意：langchain_模型配置表中没有启用状态字段，所有模型默认启用
                "启用模型数量": len(所有模型),
                "禁用模型数量": 0,
                "提供商分布": {},
                "模型类型分布": {},
                "最近测试时间": None,
                "总调用次数": sum(m.get("调用次数", 0) for m in 所有模型),
                "总成功次数": sum(m.get("成功次数", 0) for m in 所有模型),
            }

            # 统计提供商和模型类型分布
            from collections import Counter

            统计概览["提供商分布"] = dict(
                Counter(m.get("提供商", "未知") for m in 所有模型)
            )
            统计概览["模型类型分布"] = dict(
                Counter(m.get("模型类型", "未知") for m in 所有模型)
            )

            return 统一响应模型.成功(统计概览, "获取模型统计概览成功")
        else:
            return 统一响应模型.失败(1500, "获取模型统计数据失败")

    except Exception as e:
        路由日志器.error(f"获取模型统计概览异常: {str(e)}")
        return 统一响应模型.失败(1500, f"获取模型统计概览异常: {str(e)}")
