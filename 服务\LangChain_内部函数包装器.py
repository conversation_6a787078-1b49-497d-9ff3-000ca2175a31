"""
LangChain 内部函数包装器 - 简化版本
基于FastAPI依赖注入的简化内部函数调用系统

主要功能：
1. 直接使用FastAPI的@tool装饰器
2. 简化的用户id注入机制
3. 统一的错误处理和日志记录
4. 直接的数据库关联

作者：系统
创建时间：2024年
简化时间：2024年
"""


import logging
import threading
from datetime import datetime
from typing import Any, Dict, Optional

# 业务模块导入 - 常用模块
import 状态
from 数据.LangChain_工具数据层 import LangChain工具数据层实例
from 服务.异步微信服务 import 异步更新微信好友下次沟通时间服务

# LangChain组件可用性检查
try:
    import importlib.util
    LANGCHAIN_AVAILABLE = importlib.util.find_spec("langchain_core.tools") is not None
except ImportError:
    LANGCHAIN_AVAILABLE = False

# 设置日志
内部函数包装器日志器 = logging.getLogger("LangChain.内部函数包装器")

# 线程本地存储，用于传递当前用户id上下文和自定义变量
_thread_local = threading.local()




# 上下文管理函数
def 设置当前用户id(用户id: int):
    """设置当前线程的用户id上下文"""
    _thread_local.current_user_id = 用户id


def 获取当前用户id() -> int | None:
    """获取当前线程的用户id上下文"""
    return getattr(_thread_local, "current_user_id", None)


def 设置当前智能体id(智能体id: int):
    """设置当前线程的智能体id上下文"""
    _thread_local.current_agent_id = 智能体id


def 获取当前智能体id() -> int | None:
    """获取当前线程的智能体id上下文"""
    return getattr(_thread_local, "current_agent_id", None)


def 设置当前自定义变量(自定义变量: Dict[str, Any]):
    """设置当前线程的自定义变量上下文"""
    _thread_local.custom_variables = 自定义变量


def 获取当前自定义变量() -> Dict[str, Any]:
    """获取当前线程的自定义变量上下文"""
    return getattr(_thread_local, "custom_variables", {})


def 清除当前用户id():
    """清除当前线程的用户id上下文"""
    if hasattr(_thread_local, "current_user_id"):
        delattr(_thread_local, "current_user_id")


def 清除当前智能体id():
    """清除当前线程的智能体id上下文"""
    if hasattr(_thread_local, "current_agent_id"):
        delattr(_thread_local, "current_agent_id")


def 清除当前自定义变量():
    """清除当前线程的自定义变量上下文"""
    if hasattr(_thread_local, "custom_variables"):
        delattr(_thread_local, "custom_variables")


def 清除所有上下文():
    """清除当前线程的所有上下文"""
    清除当前用户id()
    清除当前智能体id()
    清除当前自定义变量()


# 废弃的自动注入参数函数已移除


# 工具调用日志记录函数
async def _记录工具调用日志(
    工具名称: str,
    用户id: Optional[int],
    智能体id: Optional[int],
    调用参数: str,
    执行结果: str,
    执行状态: str,
    执行时间: float,
):
    """记录工具调用日志到数据库"""
    try:
        # 确保数据层已初始化
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 记录工具调用日志 - 处理None值
        await LangChain工具数据层实例.记录工具调用日志(
            用户id=用户id or 0,  # 如果用户id为None，使用0作为默认值
            智能体id=智能体id or 0,  # 如果智能体id为None，使用0作为默认值
            工具名称=工具名称,
            调用参数=调用参数,
            执行结果=执行结果,
            执行状态=执行状态,
            执行时间=执行时间,
        )

        内部函数包装器日志器.debug(
            f"✅ 工具调用日志记录成功: {工具名称} (用户: {用户id})"
        )

    except Exception as e:
        内部函数包装器日志器.error(f"❌ 记录工具调用日志失败 ({工具名称}): {str(e)}")


class 简化内部函数包装器:
    """简化的内部函数包装器 - 基于FastAPI依赖注入"""

    def __init__(self):
        self.已注册工具 = {}  # 工具名称 -> 工具实例
        self.工具元数据 = {}  # 工具名称 -> 元数据
        self.已初始化 = False

    async def _验证参数并记录日志(
        self, 工具名称: str, 参数字典: dict, 错误信息: str, 开始时间: datetime
    ) -> str:
        """通用参数验证失败处理"""
        当前用户id = 获取当前用户id()
        当前智能体id = 获取当前智能体id()

        内部函数包装器日志器.error(f"❌ {错误信息}")
        执行时间 = (datetime.now() - 开始时间).total_seconds()

        await _记录工具调用日志(
            工具名称,
            当前用户id,
            当前智能体id,
            str(参数字典).replace("'", '"'),
            错误信息,
            "失败",
            执行时间,
        )
        return f"参数验证失败：{错误信息}"

    async def 初始化(self):
        """初始化内部函数包装器"""
        try:
            if self.已初始化:
                return

            内部函数包装器日志器.info("🚀 开始初始化简化内部函数包装器...")

            # 注册预定义的业务函数工具
            await self._注册预定义工具()

            # 确保工具在数据库中注册
            await self._确保工具数据库注册()

            self.已初始化 = True
            内部函数包装器日志器.info(
                f"✅ 简化内部函数包装器初始化完成，注册了 {len(self.已注册工具)} 个工具"
            )

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 内部函数包装器初始化失败: {str(e)}")
            raise

    async def _注册预定义工具(self):
        """注册预定义的业务函数工具 - 简化版本"""
        try:
            if not LANGCHAIN_AVAILABLE:
                内部函数包装器日志器.warning("⚠️ LangChain不可用，跳过工具注册")
                return

            self._注册时间查询工具()
            self._注册微信好友下次沟通时间更新工具()

            内部函数包装器日志器.info("✅ 预定义工具注册完成")

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 注册预定义工具失败: {str(e)}")




    def _注册时间查询工具(self):
        """注册时间查询工具"""

        from langchain_core.tools import tool

        @tool
        async def 获取当前时间() -> str:
            """获取当前系统时间。

            此工具用于获取服务器的当前时间戳，提供标准格式的时间信息。
            主要用于时间记录、日志标记、业务时间计算、定时任务等场景。

            Args:
                无参数: 此工具不需要任何输入参数，直接返回当前系统时间。

            Returns:
                str: 格式化的当前时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"。
                    例如: "2024-07-31 14:30:25"

            Raises:
                系统异常: 当获取系统时间过程中发生错误时

            Examples:
                # 获取当前时间
                result = await 获取当前时间()
                # 返回: "2024-07-31 14:30:25"

                # 用于时间记录
                current_time = await 获取当前时间()
                log_message = f"操作执行时间: {current_time}"

            Note:
                - 返回的时间基于服务器所在时区的本地时间
                - 时间格式固定为 "YYYY-MM-DD HH:MM:SS"，便于标准化处理
                - 此工具执行速度快，适合频繁调用
                - 所有调用都会记录到工具调用日志中
                - 可用于生成时间戳、计算时间差、验证时间范围等业务场景
                - 时间精度为秒级，不包含毫秒信息
            """
            开始时间 = None
            当前用户id = None
            当前智能体id = None
            try:
                开始时间 = datetime.now()
                当前用户id = 获取当前用户id()
                当前智能体id = 获取当前智能体id()

                结果 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                执行时间 = (datetime.now() - 开始时间).total_seconds()

                # 记录成功的工具调用日志
                await _记录工具调用日志(
                    "获取当前时间",
                    当前用户id,
                    当前智能体id,
                    "{}",
                    结果,
                    "成功",
                    执行时间,
                )

                return 结果
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 获取当前时间失败: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "获取当前时间",
                        当前用户id,
                        当前智能体id,
                        "{}",
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"获取时间失败: {str(e)}"

        # 修复：添加工具到已注册工具字典
        self.已注册工具["获取当前时间"] = 获取当前时间
        self.工具元数据["获取当前时间"] = {
            "分类": "内部工具",
            "权限要求": "",
            "描述": "获取当前系统时间",
        }

    def _注册微信好友下次沟通时间更新工具(self):
        """注册微信好友下次沟通时间更新工具 - 使用纯LangChain方式"""

        from langchain_core.tools import tool

        @tool
        async def 更新微信好友下次沟通时间(下次沟通时间: str) -> str:
            """根据用户对话内容智能设置微信好友的下次沟通时间。

            当用户在对话中提到时间相关的表达（如"明天下午3点"、"下周一"、"过两天"等），
            智能体应该解析这些自然语言时间表达，转换为具体的时间格式，然后调用此工具更新数据库。


            Args:
                用户id: 当前用户id，用于权限验证
                我方微信号id: 我方微信账号ID，指定操作哪个微信号下的好友
                识别id: 微信好友识别ID，唯一标识好友记录
                下次沟通时间: 指定的沟通时间，格式为 YYYY-MM-DD HH:MM:SS，可选

            Returns:
                str: 更新操作的结果描述，包含成功状态和时间信息

            Examples:
                用户说："明天下午3点再联系我"
                智能体应该：
                1. 解析"明天下午3点"为具体时间（如"2024-08-02 15:00:00"）
                2. 调用此工具：更新微信好友下次沟通时间(下次沟通时间="2024-08-02 15:00:00")
            """
            try:
                开始时间 = datetime.now()

                # 添加调试输出
                print("🚨🚨🚨 LangChain工具 更新微信好友下次沟通时间 被调用！")
                print(f"🔧 [LangChain工具] 智能体传递的参数: 下次沟通时间={下次沟通时间}")

                # 获取当前上下文信息（用于日志记录和参数注入）
                当前用户id = 获取当前用户id()
                当前智能体id = 获取当前智能体id()
                当前自定义变量 = 获取当前自定义变量()

                # 从线程中获取必需的参数
                用户id = 当前用户id
                我方微信号id = 当前自定义变量.get("我方微信号id") if 当前自定义变量 else None
                识别id = 当前自定义变量.get("识别id") if 当前自定义变量 else None

                print(f"🔧 [线程参数注入] 用户id={用户id}, 我方微信号id={我方微信号id}, 识别id={识别id}")
                print(f"🔧 [最终参数] 用户id={用户id}, 我方微信号id={我方微信号id}, 识别id={识别id}, 下次沟通时间={下次沟通时间}")

                # 基本参数验证
                if 用户id is None:
                    return "❌ 参数错误：用户id不能为空"
                if 我方微信号id is None:
                    return "❌ 参数错误：我方微信号id不能为空"
                if 识别id is None:
                    return "❌ 参数错误：识别id不能为空"
                if not 下次沟通时间:
                    return "❌ 参数错误：下次沟通时间不能为空"

                # 时间格式转换
                try:
                    if isinstance(下次沟通时间, str):
                        from datetime import datetime as dt
                        时间对象 = dt.strptime(下次沟通时间, "%Y-%m-%d %H:%M:%S")
                    else:
                        时间对象 = 下次沟通时间
                except ValueError as e:
                    return f"❌ 时间格式错误: {e}"



                更新结果 = await 异步更新微信好友下次沟通时间服务(
                    用户id=用户id,
                    我方微信号id=我方微信号id,
                    识别id=识别id,
                    下次沟通时间=时间对象
                )

                # 处理服务层返回结果
                if 更新结果.get("status") == 状态.通用.成功:
                    结果 = f"✅ 已设置微信好友下次沟通时间为: {时间对象.strftime('%Y-%m-%d %H:%M:%S')}"
                    内部函数包装器日志器.info(f"✅ 服务层更新成功: {更新结果.get('message')}")
                else:
                    结果 = f"❌ 更新失败: {更新结果.get('message', '未知错误')}"
                    内部函数包装器日志器.error(f"❌ 服务层更新失败: {更新结果.get('message')}")

                # 记录工具调用日志
                执行时间 = (datetime.now() - 开始时间).total_seconds()
                await _记录工具调用日志(
                    "更新微信好友下次沟通时间",
                    当前用户id,
                    当前智能体id,
                    f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}, "下次沟通时间": "{时间对象.strftime("%Y-%m-%d %H:%M:%S")}"}}',
                    结果,
                    "成功",
                    执行时间,
                )

                return 结果

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 更新微信好友下次沟通时间异常: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "更新微信好友下次沟通时间",
                        当前用户id,
                        当前智能体id,
                        f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}, "下次沟通时间": "{下次沟通时间}"}}',
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"更新微信好友下次沟通时间系统异常: {str(e)}"

        self.已注册工具["更新微信好友下次沟通时间"] = 更新微信好友下次沟通时间
        self.工具元数据["更新微信好友下次沟通时间"] = {
            "分类": "微信管理",
            "权限要求": "微信.更新",
            "描述": "根据用户对话内容智能设置微信好友的下次沟通时间，支持自然语言时间解析和自动时间计算",
        }

    async def _确保工具数据库注册(self):
        """确保工具在数据库中注册 - 使用统一工具数据层"""
        try:
            # 初始化工具数据层
            if not LangChain工具数据层实例.已初始化:
                await LangChain工具数据层实例.初始化()

            for 工具名称, 元数据 in self.工具元数据.items():
                工具配置 = {
                    "工具名称": 工具名称,
                    "工具描述": 元数据["描述"],
                    "工具参数": "",  # 内部函数工具参数通过装饰器定义
                    "权限要求": 元数据["权限要求"]
                    if isinstance(元数据["权限要求"], str)
                    else "",
                    "安全级别": 1,
                    "启用状态": True,
                }
                await LangChain工具数据层实例.创建工具配置(工具配置)

            内部函数包装器日志器.info("✅ 工具数据库注册完成")

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 工具数据库注册失败: {str(e)}")

    def 获取用户工具列表(self, 用户id: int) -> Dict[str, Any]:
        """为指定用户获取可用的工具列表"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空工具列表")
            return {}

        # 现在直接返回工具，参数注入在工具内部通过线程上下文处理
        内部函数包装器日志器.info(f"✅ 为用户 {用户id} 获取了 {len(self.已注册工具)} 个工具")
        return self.已注册工具.copy()

    async def 获取可用工具列表(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空工具列表")
            return {}
        return self.已注册工具.copy()

    async def 获取工具元数据(self) -> Dict[str, Any]:
        """获取工具元数据"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空元数据")
            return {}
        return self.工具元数据.copy()


# 创建全局实例
内部函数包装器实例 = 简化内部函数包装器()
