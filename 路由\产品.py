import base64
import binascii
import json
import traceback
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel, Field

# 导入认证依赖
from 依赖项.认证 import 获取当前用户
from 数据.异步用户产品数据 import 异步用户产品数据
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.响应模型 import 统一响应模型

# 导入统一日志系统
from 日志 import 应用日志器, 接口日志器, 错误日志器

# 导入服务层
# from 服务.产品解析服务 import 产品解析服务  # Coze相关服务已移除

# 导入状态码
from 状态 import 产品管理, 状态

# 创建服务实例
# 产品解析服务实例 = 产品解析服务()  # Coze相关服务已移除
异步用户产品数据实例 = 异步用户产品数据()

# 创建产品路由器
产品路由 = APIRouter(
    tags=["产品管理"],
    responses={404: {"description": "接口未找到"}},
)


# Pydantic模型
class 获取用户产品列表请求(BaseModel):
    productName: Optional[str] = None
    页码: int = 1
    每页条数: int = 20


class 添加用户产品请求(BaseModel):
    """添加用户产品请求模型"""

    产品名称: str
    产品分类: Optional[str] = None
    产品描述: Optional[str] = None
    产品信息: Optional[Dict[str, Any]] = None
    产品规格: Optional[Dict[str, Any]] = None


class 更新用户产品请求(BaseModel):
    产品ID: int
    产品名称: Optional[str] = None
    产品分类: Optional[str] = None
    产品描述: Optional[str] = None
    产品信息: Optional[Dict[str, Any]] = None
    产品规格: Optional[Dict[str, Any]] = None
    状态: Optional[int] = None


class 获取抖音商品列表请求(BaseModel):
    页码: int = 1
    每页条数: int = 20
    商品名称: Optional[str] = None
    平台: Optional[str] = None
    状态: Optional[int] = None


# ==================== 产品解析相关模型 ====================


class 产品信息解析请求(BaseModel):
    """产品信息解析请求模型"""

    产品信息文本_base64: str = Field(
        ..., description="Base64编码的产品信息文本", min_length=1
    )
    产品id: Optional[int] = Field(None, description="产品ID（更新时提供）", gt=0)


class 产品详情请求(BaseModel):
    """获取产品详情请求模型"""

    产品id: int = Field(..., description="产品ID", gt=0)


class 创建产品请求(BaseModel):
    """创建产品请求模型"""

    产品名称: Optional[str] = Field(None, description="产品名称")
    产品分类: Optional[str] = Field(None, description="产品分类")
    产品描述: Optional[str] = Field(None, description="产品描述")
    产品信息: Optional[Dict[str, Any]] = Field(None, description="产品详细信息")
    产品规格: Optional[Dict[str, Any]] = Field(None, description="产品规格信息")


class 删除产品请求(BaseModel):
    """删除产品请求模型"""

    产品id: int = Field(..., description="要删除的产品ID", gt=0)


class 导入知识库请求(BaseModel):
    """导入知识库请求模型"""

    套餐关联id: int = Field(..., description="用户套餐关联表id", gt=0)


class 删除产品知识库文档请求(BaseModel):
    """删除产品知识库文档请求模型"""

    产品id: int = Field(..., description="要删除知识库文档的产品ID", gt=0)
    套餐关联id: int = Field(..., description="套餐关联id", gt=0)


class 获取知识库文档列表请求(BaseModel):
    """获取知识库文档列表请求模型"""

    套餐关联id: int = Field(..., description="用户套餐关联表id", gt=0)


class 获取手卡路径请求(BaseModel):
    """获取产品手卡文件路径请求模型"""

    产品id: int = Field(..., description="产品ID", gt=0)


@产品路由.post("/user/add")
async def 添加用户产品(
    请求: 添加用户产品请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    添加用户产品

    参数:
        产品名称: 产品名称（必填）
        产品分类: 产品分类（可选）
        产品描述: 产品描述（可选）
        产品信息: 产品详细信息JSON（可选）

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 插入新产品
        插入查询 = """
        INSERT INTO 用户产品表 (用户id, 产品名称, 产品分类, 产品描述, 产品信息, 产品规格)
        VALUES ($1, $2, $3, $4, $5, $6)
        """
        params = [
            用户id,
            请求.产品名称,
            请求.产品分类,
            请求.产品描述,
            json.dumps(请求.产品信息) if 请求.产品信息 else None,
            json.dumps(请求.产品规格) if 请求.产品规格 else None,
        ]

        结果 = await 异步连接池实例.执行插入(插入查询, tuple(params))

        # 返回成功响应
        return 统一响应模型.成功({"产品ID": 结果}, "添加用户产品成功")
    except Exception as e:
        错误日志器.error(f"添加用户产品接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(产品管理.产品创建失败, f"添加用户产品失败: {str(e)}")


@产品路由.post("/user/update")
async def 更新用户产品(
    请求: 更新用户产品请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    更新用户产品信息

    参数:
        产品ID: 产品ID（必填）
        产品名称: 产品名称（可选）
        产品分类: 产品分类（可选）
        产品描述: 产品描述（可选）
        产品信息: 产品详细信息JSON（可选）
        状态: 产品状态（可选）

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]

        # 构建更新字段和参数
        update_fields = []
        params = []
        param_index = 1

        if 请求.产品名称 is not None:
            update_fields.append(f"产品名称 = ${param_index}")
            params.append(请求.产品名称)
            param_index += 1

        if 请求.产品分类 is not None:
            update_fields.append(f"产品分类 = ${param_index}")
            params.append(请求.产品分类)
            param_index += 1

        if 请求.产品描述 is not None:
            update_fields.append(f"产品描述 = ${param_index}")
            params.append(请求.产品描述)
            param_index += 1

        if 请求.产品信息 is not None:
            update_fields.append(f"产品信息 = ${param_index}")
            params.append(json.dumps(请求.产品信息))
            param_index += 1

        if 请求.产品规格 is not None:
            update_fields.append(f"产品规格 = ${param_index}")
            params.append(json.dumps(请求.产品规格))
            param_index += 1

        if 请求.状态 is not None:
            update_fields.append(f"状态 = ${param_index}")
            params.append(请求.状态)
            param_index += 1

        if not update_fields:
            return 统一响应模型.失败(400, "没有需要更新的字段")

        # 添加更新时间
        update_fields.append("更新时间 = NOW()")

        # 添加WHERE条件参数
        params.extend([请求.产品ID, 用户id])

        # 执行更新
        更新查询 = f"""
        UPDATE 用户产品表
        SET {", ".join(update_fields)}
        WHERE id = ${param_index} AND 用户id = ${param_index + 1}
        """

        影响行数 = await 异步连接池实例.执行更新(更新查询, tuple(params))

        if 影响行数 == 0:
            return 统一响应模型.失败(产品管理.产品不存在, "产品不存在或无权限修改")

        # 返回成功响应
        return 统一响应模型.成功({"产品ID": 请求.产品ID}, "更新用户产品成功")
    except Exception as e:
        错误日志器.error(f"更新用户产品接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(产品管理.产品更新失败, f"更新用户产品失败: {str(e)}")


@产品路由.post("/list")
async def 获取用户产品列表(
    请求: 获取用户产品列表请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取用户产品列表

    参数:
        请求: 包含分页和筛选条件的请求数据
        当前用户: 通过依赖注入的当前登录用户信息

    返回:
        包含产品列表和分页信息的统一响应
    """
    try:
        接口日志器.info("=== 开始获取用户产品列表 ===")

        # 获取用户id和分页参数
        用户id = 当前用户["id"]
        页码 = 请求.页码 or 1
        每页条数 = 请求.每页条数 or 10
        offset = (页码 - 1) * 每页条数

        接口日志器.info(
            f"用户id: {用户id}, 页码: {页码}, 每页条数: {每页条数}, 偏移量: {offset}"
        )

        # 调试：检查数据库中该用户的产品数量
        调试查询 = "SELECT COUNT(*) as total FROM 用户产品表 WHERE 用户id = $1"
        调试结果 = await 异步连接池实例.执行查询(调试查询, (用户id,))
        接口日志器.info(f"用户{用户id}的所有产品数量: {调试结果}")

        调试查询2 = (
            "SELECT COUNT(*) as total FROM 用户产品表 WHERE 用户id = $1 AND 状态 = 1"
        )
        调试结果2 = await 异步连接池实例.执行查询(调试查询2, (用户id,))
        接口日志器.info(f"用户{用户id}的正常状态产品数量: {调试结果2}")

        # 构建查询条件（显示所有状态的产品）
        where_conditions = ["用户id = $1"]
        params = [用户id]

        # 添加产品名称筛选
        if hasattr(请求, "productName") and 请求.productName:
            where_conditions.append("产品名称 LIKE $1")
            params.append(f"%{请求.productName}%")
            接口日志器.info(f"添加产品名称筛选: {请求.productName}")

        where_clause = " AND ".join(where_conditions)
        接口日志器.info(f"WHERE条件: {where_clause}")
        接口日志器.info(f"查询参数: {params}")

        # 查询总数
        总数查询 = f"SELECT COUNT(*) as total FROM 用户产品表 WHERE {where_clause}"
        接口日志器.info(f"执行总数查询: {总数查询}")

        总数结果 = await 异步连接池实例.执行查询(总数查询, tuple(params))
        接口日志器.info(f"总数查询结果类型: {type(总数结果)}")
        接口日志器.info(f"总数查询结果: {总数结果}")

        # 安全地获取总数
        总数 = 0
        if 总数结果 and len(总数结果) > 0:
            第一行 = 总数结果[0]
            接口日志器.info(f"总数第一行类型: {type(第一行)}, 内容: {第一行}")

            if isinstance(第一行, dict):
                总数 = 第一行.get("total", 0)
                接口日志器.info(f"从字典获取总数: {总数}")
            elif isinstance(第一行, (tuple, list)) and len(第一行) > 0:
                总数 = 第一行[0]
                接口日志器.info(f"从元组获取总数: {总数}")
            else:
                接口日志器.warning(f"未知的总数数据格式: {type(第一行)}")
                总数 = 0

        接口日志器.info(f"最终总数: {总数}")

        # 查询产品列表
        列表查询 = f"""
        SELECT id, 产品名称, 产品分类, 产品描述, 状态, 创建时间, 更新时间
        FROM 用户产品表
        WHERE {where_clause}
        ORDER BY 创建时间 DESC
        LIMIT $1 OFFSET $2
        """
        列表参数 = params + [每页条数, offset]
        接口日志器.info(f"执行列表查询: {列表查询}")
        接口日志器.info(f"列表查询参数: {列表参数}")

        产品列表 = await 异步连接池实例.执行查询(列表查询, tuple(列表参数))
        接口日志器.info(f"产品列表查询结果类型: {type(产品列表)}")
        接口日志器.info(f"产品列表数量: {len(产品列表) if 产品列表 else 0}")

        # 处理产品列表数据（包含知识库状态）
        格式化列表 = []
        if 产品列表:
            for 索引, 产品 in enumerate(产品列表):
                接口日志器.debug(
                    f"处理第{索引 + 1}个产品，类型: {type(产品)}, 内容: {产品}"
                )

                try:
                    if isinstance(产品, dict):
                        # 字典格式，直接使用
                        创建时间 = 产品.get("创建时间")
                        更新时间 = 产品.get("更新时间")
                        产品id = 产品.get("id")

                        格式化产品 = {
                            "id": 产品id,
                            "产品名称": 产品.get("产品名称", ""),
                            "产品分类": 产品.get("产品分类", ""),
                            "产品描述": 产品.get("产品描述", ""),
                            "状态": 产品.get("状态", 1),
                            "创建时间": 创建时间.strftime("%Y-%m-%d %H:%M:%S")
                            if 创建时间
                            else "",
                            "更新时间": 更新时间.strftime("%Y-%m-%d %H:%M:%S")
                            if 更新时间
                            else "",
                        }
                    elif isinstance(产品, (tuple, list)) and len(产品) >= 7:
                        # 元组格式，按顺序解析
                        产品id = 产品[0]
                        格式化产品 = {
                            "产品id": 产品id,
                            "产品名称": 产品[1] or "",
                            "产品分类": 产品[2] or "",
                            "产品描述": 产品[3] or "",
                            "状态": 产品[4] or 1,
                            "创建时间": 产品[5].strftime("%Y-%m-%d %H:%M:%S")
                            if 产品[5]
                            else "",
                            "更新时间": 产品[6].strftime("%Y-%m-%d %H:%M:%S")
                            if 产品[6]
                            else "",
                        }
                    else:
                        接口日志器.error(
                            f"未知的产品数据格式: {type(产品)}, 内容: {产品}"
                        )
                        continue

                    # 查询该产品的知识库文档状态
                    if 产品id:
                        # 知识库数据服务已移除
                        格式化产品["知识库状态"] = {"已提交": False, "文档数量": 0}
                    else:
                        格式化产品["知识库状态"] = {
                            "已提交": False,
                            "文档数量": 0,
                            "最新提交时间": None,
                            "文档名称列表": [],
                        }

                    格式化列表.append(格式化产品)
                    接口日志器.debug(
                        f"成功格式化产品: {格式化产品['产品名称']}, 知识库状态: {格式化产品['知识库状态']['已提交']}"
                    )

                except Exception as 格式化错误:
                    接口日志器.error(
                        f"格式化第{索引 + 1}个产品时出错: {str(格式化错误)}, 产品数据: {产品}"
                    )
                    continue

        # 构建返回结果
        结果数据 = {
            "列表": 格式化列表,
            "总数": 总数,
            "页码": 页码,
            "每页条数": 每页条数,
        }

        接口日志器.info("=== 产品列表获取完成 ===")
        接口日志器.info(
            f"返回数据: 列表数量={len(格式化列表)}, 总数={总数}, 页码={页码}"
        )

        return 统一响应模型.成功(结果数据, "获取产品列表成功")

    except Exception as e:
        return 统一响应模型.失败(1001, f"获取产品列表失败: {str(e)}")


@产品路由.post("/submit-to-knowledge")
async def 提交产品到知识库(
    请求: dict = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    提交单个产品到知识库

    参数:
        产品id: 产品ID
        产品名称: 产品名称
        产品描述: 产品描述
        产品分类: 产品分类

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]
        产品id = 请求.get("产品id")

        if not 产品id:
            return 统一响应模型.失败(400, "产品ID不能为空")

        # 验证产品是否属于当前用户
        验证查询 = "SELECT id, 产品名称 FROM 用户产品表 WHERE id = $1 AND 用户id = $2"
        产品信息 = await 异步连接池实例.执行查询(验证查询, (产品id, 用户id))

        if not 产品信息:
            return 统一响应模型.失败(404, "产品不存在或无权限操作")

        # 知识库服务已移除，直接返回错误
        return 统一响应模型.失败(501, "知识库服务已停用，此功能暂时不可用")

    except Exception as e:
        错误日志器.error(f"提交产品到知识库接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"提交产品到知识库失败: {str(e)}")


@产品路由.post("/resubmit-to-knowledge")
async def 重新提交产品到知识库(
    请求: dict = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    重新提交产品到知识库（覆盖现有文档）

    参数:
        产品id: 产品ID
        产品名称: 产品名称
        知识id: 知识id（可选）
        强制覆盖: 是否强制覆盖现有文档

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]
        产品id = 请求.get("产品id")

        if not 产品id:
            return 统一响应模型.失败(400, "产品ID不能为空")

        # 验证产品是否属于当前用户
        验证查询 = "SELECT id, 产品名称 FROM 用户产品表 WHERE id = $1 AND 用户id = $2"
        产品信息 = await 异步连接池实例.执行查询(验证查询, (产品id, 用户id))

        if not 产品信息:
            return 统一响应模型.失败(404, "产品不存在或无权限操作")

        # 知识库服务已移除，直接返回错误
        return 统一响应模型.失败(501, "知识库服务已停用，此功能暂时不可用")

    except Exception as e:
        错误日志器.error(f"重新提交产品到知识库接口异常: {str(e)}")
        return 统一响应模型.失败(500, f"重新提交产品到知识库失败: {str(e)}")


@产品路由.post("/user/delete")
async def 删除用户产品(请求: dict = Body(...), 当前用户: dict = Depends(获取当前用户)):
    """
    删除用户产品

    参数:
        产品ID: 产品ID

    返回:
        操作结果的统一响应
    """
    try:
        # 从认证中获取用户id
        用户id = 当前用户["id"]
        产品ID = 请求.get("产品ID") or 请求.get("产品id")

        # 删除产品
        删除查询 = "DELETE FROM 用户产品表 WHERE id = $1 AND 用户id = $2"
        影响行数 = await 异步连接池实例.执行更新(删除查询, (产品ID, 用户id))

        if 影响行数 == 0:
            return 统一响应模型.失败(404, "产品不存在或无权限删除")

        # 返回成功响应
        return 统一响应模型.成功({"产品ID": 产品ID}, "删除用户产品成功")
    except Exception as e:
        错误日志器.error(f"删除用户产品接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"删除用户产品失败: {str(e)}")


@产品路由.post("/douyin/list")
async def 获取抖音商品列表(
    请求: 获取抖音商品列表请求 = Body(...), _当前用户: dict = Depends(获取当前用户)
):
    """
    获取抖音商品列表

    参数:
        页码: 当前页码，默认为1
        每页条数: 每页显示条数，默认为20
        商品名称: 可选，商品名称搜索
        平台: 可选，平台筛选
        状态: 可选，商品状态筛选

    返回:
        包含抖音商品列表的统一响应
    """
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if 请求.商品名称:
            where_conditions.append(
                "商品信息->>'$.商品名称' LIKE $1 OR 商品信息->>'$.title' LIKE $2"
            )
            params.extend([f"%{请求.商品名称}%", f"%{请求.商品名称}%"])

        if 请求.平台:
            where_conditions.append("商品平台 = $3")
            params.append(请求.平台)

        if 请求.状态 is not None:
            where_conditions.append("状态 = $4")
            params.append(请求.状态)

        where_clause = " AND ".join(where_conditions)

        # 查询总数
        总数查询 = f"SELECT COUNT(*) as total FROM 抖音商品表 WHERE {where_clause}"
        总数结果 = await 异步连接池实例.执行查询(总数查询, tuple(params))

        # 安全地获取总数
        总数 = 0
        if 总数结果:
            第一行 = 总数结果[0]
            if isinstance(第一行, dict):
                总数 = 第一行.get("total", 0)
            elif isinstance(第一行, (tuple, list)) and len(第一行) > 0:
                总数 = 第一行[0]

        # 查询列表数据
        offset = (请求.页码 - 1) * 请求.每页条数
        列表查询 = f"""
        SELECT id, 商品id, 商品平台, 商品信息, 状态, 创建时间, 更新时间
        FROM 抖音商品表 
        WHERE {where_clause}
        ORDER BY 创建时间 DESC 
        LIMIT $1 OFFSET $2
        """
        列表结果 = await 异步连接池实例.执行查询(
            列表查询, tuple(params + [请求.每页条数, offset])
        )

        # 构建返回数据
        结果 = {
            "列表": 列表结果 or [],
            "总数": 总数,
            "当前页": 请求.页码,
            "每页条数": 请求.每页条数,
            "总页数": (总数 + 请求.每页条数 - 1) // 请求.每页条数,
        }

        # 返回成功响应
        return 统一响应模型.成功(结果, "获取抖音商品列表成功")
    except Exception as e:
        错误日志器.error(f"获取抖音商品列表接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取抖音商品列表失败: {str(e)}")


# ==================== 样品管理接口 ====================


class 获取样品列表请求(BaseModel):
    页码: int = 1
    每页条数: int = 20
    收件人: Optional[str] = None
    产品ID: Optional[int] = None

    审核状态: Optional[int] = None
    快递状态: Optional[int] = None


class 添加样品请求(BaseModel):
    收件人: str
    地址: str
    电话: str
    产品ID: int

    数量: int
    规格: Optional[str] = None
    寄样备注: Optional[str] = None


class 更新样品请求(BaseModel):
    样品id: int
    收件人: Optional[str] = None
    地址: Optional[str] = None
    电话: Optional[str] = None
    产品ID: Optional[int] = None
    数量: Optional[int] = None
    规格: Optional[str] = None
    寄样备注: Optional[str] = None
    审核状态: Optional[int] = None
    审核备注: Optional[str] = None
    快递单号: Optional[str] = None
    快递状态: Optional[int] = None


@产品路由.post("/samples/list")
async def 获取样品列表(
    请求: 获取样品列表请求 = Body(...), _当前用户: dict = Depends(获取当前用户)
):
    """
    获取样品信息记录列表

    参数:
        页码: 当前页码，默认为1
        每页条数: 每页显示条数，默认为20
        收件人: 可选，收件人姓名搜索
        产品ID: 可选，产品ID筛选
        审核状态: 可选，审核状态筛选
        快递状态: 可选，快递状态筛选

    返回:
        包含样品列表的统一响应
    """
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if 请求.收件人:
            where_conditions.append("收件人 LIKE $1")
            params.append(f"%{请求.收件人}%")

        if 请求.产品ID:
            where_conditions.append("用户产品表id = $1")
            params.append(请求.产品ID)

        if 请求.审核状态 is not None:
            where_conditions.append("用户审核状态 = $1")
            params.append(请求.审核状态)

        if 请求.快递状态 is not None:
            where_conditions.append("快递状态 = $1")
            params.append(请求.快递状态)

        where_clause = " AND ".join(where_conditions)

        # 查询总数
        总数查询 = f"SELECT COUNT(*) as total FROM 样品信息记录表 WHERE {where_clause}"
        总数结果 = await 异步连接池实例.执行查询(总数查询, tuple(params))

        # 安全地获取总数，处理可能的数据格式问题
        总数 = 0
        if 总数结果:
            第一行 = 总数结果[0]
            if isinstance(第一行, dict):
                总数 = 第一行.get("total", 0)
            elif isinstance(第一行, (tuple, list)) and len(第一行) > 0:
                总数 = 第一行[0]
            else:
                总数 = 0

        # 查询列表数据（关联用户产品表获取产品信息）
        offset = (请求.页码 - 1) * 请求.每页条数
        列表查询 = f"""
        SELECT
            s.id, s.收件人, s.地址, s.电话, s.寄样备注, s.用户产品表id, s.数量, s.规格,
            s.用户审核状态, s.负责人审核状态, s.审核人ID, s.审核备注, s.审核时间,
            s.快递单号, s.快递状态, s.快递状态标识, s.快递状态描述, s.快递状态变更时间, s.快递公司,
            s.创建时间, s.更新时间,
            p.产品名称, p.产品分类, p.产品信息
        FROM 样品信息记录表 s
        LEFT JOIN 用户产品表 p ON s.用户产品表id = p.id
        WHERE {where_clause}
        ORDER BY s.创建时间 DESC
        LIMIT $1 OFFSET $2
        """
        列表结果 = await 异步连接池实例.执行查询(
            列表查询, tuple(params + [请求.每页条数, offset])
        )

        # 构建返回数据
        结果 = {
            "列表": 列表结果 or [],
            "总数": 总数,
            "当前页": 请求.页码,
            "每页条数": 请求.每页条数,
            "总页数": (总数 + 请求.每页条数 - 1) // 请求.每页条数,
        }

        # 返回成功响应
        return 统一响应模型.成功(结果, "获取样品列表成功")
    except Exception as e:
        错误日志器.error(f"获取样品列表接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"获取样品列表失败: {str(e)}")


@产品路由.post("/samples/add")
async def 添加样品记录(
    请求: 添加样品请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    添加样品记录

    参数:
        收件人: 收件人姓名（必填）
        地址: 收件地址（必填）
        电话: 联系电话（必填）
        产品ID: 产品ID（必填）
        数量: 样品数量（必填）
        规格: 样品规格（可选）
        寄样备注: 寄样备注（可选）

    返回:
        操作结果的统一响应
    """
    try:
        # 验证产品是否存在
        用户id = 当前用户["id"]
        产品验证查询 = "SELECT id FROM 用户产品表 WHERE id = $1 AND 用户id = $2"
        产品验证结果 = await 异步连接池实例.执行查询(
            产品验证查询, (请求.产品ID, 用户id)
        )

        if not 产品验证结果:
            return 统一响应模型.失败(404, "产品不存在或无权限操作")

        # 插入样品记录
        插入查询 = """
        INSERT INTO 样品信息记录表 (收件人, 地址, 电话, 寄样备注, 用户产品表id, 数量, 规格, 用户审核状态)
        VALUES ($1, $2, $3, $4, $5, $6, $7, 0)
        """
        params = [
            请求.收件人,
            请求.地址,
            请求.电话,
            请求.寄样备注,
            请求.产品ID,
            请求.数量,
            请求.规格,
        ]

        结果 = await 异步连接池实例.执行插入(插入查询, tuple(params))

        # 返回成功响应
        return 统一响应模型.成功({"样品id": 结果}, "添加样品记录成功")
    except Exception as e:
        错误日志器.error(f"添加样品记录接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"添加样品记录失败: {str(e)}")


@产品路由.post("/samples/update")
async def 更新样品记录(
    请求: 更新样品请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    更新样品记录信息

    参数:
        样品id: 样品记录ID（必填）
        其他字段为可选更新字段

    返回:
        操作结果的统一响应
    """
    try:
        # 构建更新字段和参数
        update_fields = []
        params = []
        param_index = 1

        if 请求.收件人 is not None:
            update_fields.append(f"收件人 = ${param_index}")
            params.append(请求.收件人)
            param_index += 1

        if 请求.地址 is not None:
            update_fields.append(f"地址 = ${param_index}")
            params.append(请求.地址)
            param_index += 1

        if 请求.电话 is not None:
            update_fields.append(f"电话 = ${param_index}")
            params.append(请求.电话)
            param_index += 1

        if 请求.产品ID is not None:
            update_fields.append(f"用户产品表id = ${param_index}")
            params.append(请求.产品ID)
            param_index += 1

        if 请求.数量 is not None:
            update_fields.append(f"数量 = ${param_index}")
            params.append(请求.数量)
            param_index += 1

        if 请求.规格 is not None:
            update_fields.append(f"规格 = ${param_index}")
            params.append(请求.规格)
            param_index += 1

        if 请求.寄样备注 is not None:
            update_fields.append(f"寄样备注 = ${param_index}")
            params.append(请求.寄样备注)
            param_index += 1

        if 请求.审核状态 is not None:
            update_fields.append(f"用户审核状态 = ${param_index}")
            params.append(请求.审核状态)
            param_index += 1
            update_fields.append(f"审核人ID = ${param_index}")
            params.append(当前用户["id"])
            param_index += 1
            update_fields.append("审核时间 = NOW()")

        if 请求.审核备注 is not None:
            update_fields.append(f"审核备注 = ${param_index}")
            params.append(请求.审核备注)
            param_index += 1

        if 请求.快递单号 is not None:
            update_fields.append(f"快递单号 = ${param_index}")
            params.append(请求.快递单号)
            param_index += 1

        if 请求.快递状态 is not None:
            update_fields.append(f"快递状态 = ${param_index}")
            params.append(请求.快递状态)
            param_index += 1
            update_fields.append("快递状态变更时间 = NOW()")

        if not update_fields:
            return 统一响应模型.失败(400, "没有需要更新的字段")

        # 添加更新时间
        update_fields.append("更新时间 = NOW()")

        # 添加WHERE条件参数
        params.append(请求.样品id)

        # 执行更新
        更新查询 = f"""
        UPDATE 样品信息记录表
        SET {", ".join(update_fields)}
        WHERE id = ${param_index}
        """

        影响行数 = await 异步连接池实例.执行更新(更新查询, tuple(params))

        if 影响行数 == 0:
            return 统一响应模型.失败(404, "样品记录不存在")

        # 返回成功响应
        return 统一响应模型.成功({"样品id": 请求.样品id}, "更新样品记录成功")
    except Exception as e:
        错误日志器.error(f"更新样品记录接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"更新样品记录失败: {str(e)}")


@产品路由.post("/samples/delete")
async def 删除样品记录(请求: dict = Body(...), _当前用户: dict = Depends(获取当前用户)):
    """
    删除样品记录

    参数:
        样品id: 样品记录ID

    返回:
        操作结果的统一响应
    """
    try:
        样品id = 请求.get("样品id") or 请求.get("样品id")

        # 删除样品记录
        删除查询 = "DELETE FROM 样品信息记录表 WHERE id = $1"
        影响行数 = await 异步连接池实例.执行更新(删除查询, (样品id,))

        if 影响行数 == 0:
            return 统一响应模型.失败(404, "样品记录不存在")

        # 返回成功响应
        return 统一响应模型.成功({"样品id": 样品id}, "删除样品记录成功")
    except Exception as e:
        错误日志器.error(f"删除样品记录接口异常: {str(e)}")
        # 返回失败响应
        return 统一响应模型.失败(500, f"删除样品记录失败: {str(e)}")


# ==================== 用户样品申请接口 ====================


class 用户样品申请请求(BaseModel):
    """用户样品申请请求模型"""

    产品ID: int = Field(..., description="产品ID")
    收件人: str = Field(..., description="收件人姓名")
    地址: str = Field(..., description="收件地址")
    电话: str = Field(..., description="联系电话")
    数量: int = Field(1, description="申请数量，默认为1")
    规格: Optional[str] = Field(None, description="样品规格要求")
    寄样备注: Optional[str] = Field(None, description="申请备注说明")


@产品路由.post("/samples/apply")
async def 用户样品申请(
    请求: 用户样品申请请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    用户样品申请接口

    功能：
        用户申请产品样品，提交申请后等待审核

    参数:
        产品ID: 要申请样品的产品ID（必填）
        收件人: 收件人姓名（必填）
        地址: 收件地址（必填）
        电话: 联系电话（必填）
        数量: 申请数量，默认为1（可选）
        规格: 样品规格要求（可选）
        寄样备注: 申请备注说明（可选）

    返回:
        操作结果的统一响应，包含申请ID和申请时间
    """
    try:
        # 获取当前用户id
        用户id = 当前用户["id"]

        # 验证产品是否存在且属于当前用户
        产品验证查询 = """
        SELECT id, 产品名称, 状态
        FROM 用户产品表
        WHERE id = $1 AND 用户id = $2 AND 状态 = 1
        """
        产品验证结果 = await 异步连接池实例.执行查询(
            产品验证查询, (请求.产品ID, 用户id)
        )

        if not 产品验证结果:
            应用日志器.warning(
                f"用户样品申请失败: 产品不存在或无权限 - 用户id: {用户id}, 产品ID: {请求.产品ID}"
            )
            return 统一响应模型.失败(404, "产品不存在或无权限操作")

        产品信息 = 产品验证结果[0]

        # 参数验证
        if 请求.数量 <= 0:
            return 统一响应模型.失败(400, "申请数量必须大于0")

        if 请求.数量 > 10:  # 限制单次申请数量
            return 统一响应模型.失败(400, "单次申请数量不能超过10个")

        # 检查用户是否开启了自动审核功能
        自动审核查询 = """
        SELECT 是否自动审核
        FROM 用户表
        WHERE id = $1
        """
        自动审核结果 = await 异步连接池实例.执行查询(自动审核查询, (用户id,))

        # 确定审核状态和审核信息
        是否自动审核 = 自动审核结果[0].get("是否自动审核", 0) if 自动审核结果 else 0

        if 是否自动审核 == 1:
            # 自动审核：直接设置为通过状态
            用户审核状态 = 1  # 通过审核
            # 审核人ID = 用户id  # 审核人设置为申请用户自己  # 暂时不使用
            # 审核时间 = "NOW()"  # 暂时不使用
            审核备注 = "自动审核通过"
            申请状态文本 = "自动审核通过"
            应用日志器.info(
                f"样品申请自动审核通过: 用户id={用户id}, 产品ID={请求.产品ID}"
            )
        else:
            # 手动审核：设置为待审核状态
            用户审核状态 = 0  # 待审核
            申请状态文本 = "待审核"

        # 插入样品申请记录
        if 是否自动审核 == 1:
            # 自动审核的插入语句
            插入查询 = """
            INSERT INTO 样品信息记录表
            (收件人, 地址, 电话, 寄样备注, 用户产品表id, 数量, 规格,
             用户审核状态, 负责人审核状态, 审核人ID, 审核时间, 审核备注, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 0, $9, NOW(), $10, NOW())
            """
            插入参数 = [
                请求.收件人,
                请求.地址,
                请求.电话,
                请求.寄样备注,
                请求.产品ID,
                请求.数量,
                请求.规格,
                用户审核状态,
                用户id,  # 审核人ID
                审核备注,
            ]
        else:
            # 手动审核的插入语句
            插入查询 = """
            INSERT INTO 样品信息记录表
            (收件人, 地址, 电话, 寄样备注, 用户产品表id, 数量, 规格,
             用户审核状态, 负责人审核状态, 创建时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, 0, 0, NOW())
            """
            插入参数 = [
                请求.收件人,
                请求.地址,
                请求.电话,
                请求.寄样备注,
                请求.产品ID,
                请求.数量,
                请求.规格,
            ]

        # 执行插入操作
        样品申请ID = await 异步连接池实例.执行插入(插入查询, tuple(插入参数))

        # 记录申请日志
        审核状态日志 = "自动审核通过" if 是否自动审核 == 1 else "待手动审核"
        应用日志器.info(
            f"用户样品申请成功: 用户id={用户id}, 产品ID={请求.产品ID}, 产品名称={产品信息['产品名称']}, 样品申请ID={样品申请ID}, 申请数量={请求.数量}, 审核状态={审核状态日志}"
        )

        # 构建返回数据
        返回数据 = {
            "样品申请ID": 样品申请ID,
            "产品名称": 产品信息["产品名称"],
            "申请时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "申请状态": 申请状态文本,
            "是否自动审核": 是否自动审核 == 1,
            "审核说明": "已自动通过审核，可直接安排寄样"
            if 是否自动审核 == 1
            else "申请已提交，等待审核",
        }

        # 返回成功响应
        成功消息 = (
            "样品申请提交成功并自动通过审核"
            if 是否自动审核 == 1
            else "样品申请提交成功，请等待审核"
        )
        return 统一响应模型.成功(返回数据, 成功消息)

    except Exception as e:
        # 记录错误日志
        错误详情 = traceback.format_exc()
        错误日志器.error(f"用户样品申请接口异常: {str(e)}")
        错误日志器.error(f"详细错误信息: {错误详情}")

        # 返回失败响应
        return 统一响应模型.失败(500, f"样品申请提交失败: {str(e)}")


# ==================== 产品解析接口 ====================


@产品路由.post(
    "/parse",
    summary="解析并保存产品信息",
    description="接收Base64编码的产品信息，解码后通过AI解析，并保存或更新到数据库。",
)
async def 解析并保存产品信息(
    请求数据: 产品信息解析请求, 用户: dict = Depends(获取当前用户)
):
    """
    处理产品信息解析和保存的接口。
    """
    用户id = 用户["id"]  # 确保用户id不为None
    待处理的产品id = 请求数据.产品id  # 使用更明确的变量名

    # 记录请求信息
    接口日志器.info(
        f"接收到产品解析保存请求: 用户id={用户id}, 待处理产品ID={待处理的产品id}"
    )

    # 步骤 1: 解码 Base64 输入
    try:
        产品信息文本 = base64.b64decode(请求数据.产品信息文本_base64).decode("utf-8")
        接口日志器.debug(f"Base64 解码成功，文本长度: {len(产品信息文本)}")
    except (binascii.Error, UnicodeDecodeError) as e:
        错误消息 = f"产品信息文本解码失败: {e}"
        错误日志器.error(f"{错误消息}, 用户id={用户id}")
        # 返回业务状态码响应
        return 统一响应模型.失败(产品管理.产品信息格式错误, 错误消息)

    # 步骤 2: 调用 AI 解析服务
    try:
        # 产品解析服务已移除，返回错误
        return 统一响应模型.失败(501, "AI产品解析服务已停用，此功能暂时不可用")

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"AI产品解析接口异常: {e}\n{错误详情}", exc_info=True)
        return 统一响应模型.失败(500, f"服务器内部错误: {e}")


@产品路由.post("/detail", summary="获取产品详情", description="获取指定产品的详细信息")
async def 获取产品详情(请求数据: 产品详情请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品详情接口 (POST)

    根据产品ID获取产品的详细信息
    """
    try:
        # 从请求数据中获取产品ID
        产品id = 请求数据.产品id

        # 调用数据层获取产品详情
        详情结果 = await 异步用户产品数据实例.获取产品详情(
            产品id=产品id, 用户id=用户["id"]
        )

        # 检查响应格式和状态
        if not isinstance(详情结果, dict):
            # 兼容旧版返回格式
            if not 详情结果[0]:  # 检查成功标志
                # 如果获取详情失败
                return 统一响应模型.失败(
                    状态码=状态.通用.失败,
                    消息=详情结果[2],  # 使用数据层返回的错误消息
                ).转字典()
            产品详情 = 详情结果[1] if 详情结果[0] else {}
        else:
            # 新版返回格式
            if 详情结果.get("status") != 100:
                return 统一响应模型.失败(
                    状态码=详情结果.get("status", 状态.通用.失败),
                    消息=详情结果.get("message", "获取产品详情失败"),
                ).转字典()
            产品详情 = 详情结果.get("data", {})

        # 返回结果
        return 统一响应模型.成功(数据=产品详情, 消息="获取产品详情成功").转字典()

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取产品详情时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息=f"获取产品详情时发生错误: {str(e)}"
        ).转字典()


@产品路由.post(
    "/user/detail",
    summary="获取产品详情（兼容接口）",
    description="获取指定产品的详细信息",
)
async def 获取用户产品详情(请求: dict = Body(...), 用户: dict = Depends(获取当前用户)):
    """
    获取产品详情接口 (POST) - 兼容旧版前端调用

    根据产品ID获取产品的详细信息
    """
    try:
        # 从请求数据中获取产品ID（兼容多种字段名）
        产品id = 请求.get("产品ID") or 请求.get("产品id") or 请求.get("productId")

        if not 产品id:
            return 统一响应模型.失败(
                状态码=状态.通用.参数错误, 消息="缺少产品ID参数"
            ).转字典()

        # 调用数据层获取产品详情
        详情结果 = await 异步用户产品数据实例.获取产品详情(
            产品id=产品id, 用户id=用户["id"]
        )

        # 检查响应格式和状态
        if not isinstance(详情结果, dict):
            # 兼容旧版返回格式
            if not 详情结果[0]:  # 检查成功标志
                # 如果获取详情失败
                return 统一响应模型.失败(
                    状态码=状态.通用.失败,
                    消息=详情结果[2],  # 使用数据层返回的错误消息
                ).转字典()
            产品详情 = 详情结果[1] if 详情结果[0] else {}
        else:
            # 新版返回格式
            if 详情结果.get("status") != 100:
                return 统一响应模型.失败(
                    状态码=详情结果.get("status", 状态.通用.失败),
                    消息=详情结果.get("message", "获取产品详情失败"),
                ).转字典()
            产品详情 = 详情结果.get("data", {})

        # 返回结果
        return 统一响应模型.成功(数据=产品详情, 消息="获取产品详情成功").转字典()

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取产品详情时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息=f"获取产品详情时发生错误: {str(e)}"
        ).转字典()


@产品路由.post(
    "/import-knowledge",
    summary="导入产品到知识库",
    description="将用户的所有产品信息导入到知识库",
)
async def 导入产品到知识库(
    请求数据: 导入知识库请求, 用户: dict = Depends(获取当前用户)
):
    """
    导入产品到知识库接口

    将用户的所有产品信息导入到指定的知识库中
    """
    # 产品解析服务已移除，返回错误
    return 统一响应模型.失败(501, "产品导入知识库服务已停用，此功能暂时不可用").转字典()


@产品路由.post("/create", summary="创建产品", description="手动创建新产品")
async def 创建产品(
    请求数据: Optional[创建产品请求] = None, 用户: dict = Depends(获取当前用户)
):
    """
    处理创建新产品的接口。
    """
    用户id = 用户["id"]  # 确保用户id不为None
    接口日志器.info(f"接收到创建产品请求: 用户id={用户id}")

    try:
        # 创建默认请求字典
        请求字典 = {}

        # 如果有请求数据，则将其转换为字典
        if 请求数据:
            请求字典 = 请求数据.model_dump(exclude_unset=True)  # 只包含用户传入的字段

        # 设置默认产品名称（如果没有提供）
        if not 请求字典.get("产品名称"):
            请求字典["产品名称"] = f"新产品-{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 产品解析服务已移除，返回错误
        return 统一响应模型.失败(501, "产品解析服务已停用，此功能暂时不可用").转字典()

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"创建产品时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息=f"创建产品时发生错误: {str(e)}"
        ).转字典()


@产品路由.post("/delete", summary="删除产品", description="删除指定的产品")
async def 删除产品(请求数据: 删除产品请求, 用户: dict = Depends(获取当前用户)):
    """
    删除产品接口 (POST)

    根据产品ID删除指定的产品
    """
    try:
        # 从请求数据中获取产品ID
        产品id = 请求数据.产品id

        # 记录操作日志
        应用日志器.info(f"用户 {用户['id']} 请求删除产品: {产品id}")

        # 产品解析服务已移除，返回错误
        return 统一响应模型.失败(501, "产品删除服务已停用，此功能暂时不可用").转字典()

    except Exception as e:
        # 详细记录错误信息
        错误详情 = traceback.format_exc()
        错误日志器.error(f"删除产品时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息=f"删除产品时发生错误: {str(e)}"
        ).转字典()


@产品路由.post(
    "/knowledge-status",
    summary="获取产品知识库状态",
    description="通过用户套餐关联id获取产品列表及知识库文档状态",
)
async def 获取产品知识库状态(
    请求数据: 获取知识库文档列表请求, 用户: dict = Depends(获取当前用户)
):
    """
    获取产品知识库状态接口

    通过用户套餐关联id获取产品列表及知识库文档状态
    """
    用户id = 用户["id"]  # 确保用户id不为None
    套餐关联id = 请求数据.套餐关联id

    接口日志器.info(
        f"接收到获取产品知识库状态请求: 用户id={用户id}, 套餐关联id={套餐关联id}"
    )

    try:
        # 产品解析服务已移除，返回错误
        return 统一响应模型.失败(501, "产品知识库状态查询服务已停用，此功能暂时不可用").转字典()

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取产品知识库状态时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息=f"获取产品知识库状态时发生错误: {str(e)}"
        ).转字典()


@产品路由.post(
    "/get-card-path",
    summary="获取产品手卡文件路径",
    description="获取指定产品的手卡文件本地路径",
)
async def 获取产品手卡文件路径(
    请求数据: 获取手卡路径请求, 用户: dict = Depends(获取当前用户)
):
    """
    获取指定产品的手卡文件本地路径
    """
    用户id = 用户["id"]  # 确保用户id不为None

    接口日志器.info(
        f"接收到获取手卡文件路径请求: 用户id={用户id}, 产品ID={请求数据.产品id}"
    )

    try:
        # 产品解析服务已移除，返回错误
        return 统一响应模型.失败(501, "手卡文件路径获取服务已停用，此功能暂时不可用").转字典()

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"获取产品手卡文件路径时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息=f"获取产品手卡文件路径时发生错误: {str(e)}",
        ).转字典()


@产品路由.post(
    "/delete-knowledge-doc",
    summary="删除产品知识库文档",
    description="删除指定产品的知识库文档",
)
async def 删除产品知识库文档(
    请求数据: 删除产品知识库文档请求, 用户: dict = Depends(获取当前用户)
):
    """
    删除产品知识库文档接口

    删除指定产品在知识库中的文档
    """
    用户id = 用户["id"]  # 确保用户id不为None
    产品id = 请求数据.产品id
    套餐关联id = 请求数据.套餐关联id

    接口日志器.info(
        f"接收到删除产品知识库文档请求: 用户id={用户id}, 产品ID={产品id}, 套餐关联id={套餐关联id}"
    )

    try:
        # 产品解析服务已移除，返回错误
        return 统一响应模型.失败(501, "产品知识库文档删除服务已停用，此功能暂时不可用").转字典()

    except Exception as e:
        错误详情 = traceback.format_exc()
        错误日志器.error(f"删除产品知识库文档时发生异常: {str(e)}")
        错误日志器.error(f"详细错误: {错误详情}")

        # 返回统一错误响应
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息=f"删除产品知识库文档时发生错误: {str(e)}"
        ).转字典()
