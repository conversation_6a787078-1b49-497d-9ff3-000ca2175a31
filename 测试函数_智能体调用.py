#!/usr/bin/env python3
"""
智能体自动参数注入测试脚本
直接调用智能体服务函数进行测试
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 服务.LangChain_智能体服务 import LangChain智能体服务实例


async def 测试自动参数注入():


    对话结果 = await LangChain智能体服务实例.智能体对话(
            智能体id=5,
            用户表id=3,  # 测试用户ID
            用户消息="现在几点了？请帮我设置微信好友的下次沟通时间为明天下午3点",
            会话id="test-tool-call-direct",
            自定义变量={"我方微信号id": 6, "识别id": 1}
        )

    print(f"📊 对话结果: {对话结果}")



async def main():
    """主函数"""

    await 测试自动参数注入()
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
