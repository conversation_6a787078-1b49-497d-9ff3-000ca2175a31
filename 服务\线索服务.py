import inspect  # 确保导入
import json
import time  # 新增导入
from datetime import datetime  # 新增导入
from typing import Any, Dict, List, Optional

from fastapi import HTTPException, status

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据 import 线索数据操作  # 整个模块导入

from 数据模型.线索模型 import (
    线索上传请求模型,
    线索列表项响应模型,
    线索更新请求模型,
    线索详情响应模型,
    联系方式详情模型,
    获取线索列表请求模型,
)
from 日志 import 应用日志器, 错误日志器
from 服务.数据隐私服务 import 数据隐私服务实例


async def 服务_上传新线索(请求: 线索上传请求模型, 操作用户id: int) -> Dict[str, Any]:
    """
    服务层核心逻辑：上传新线索。
    1. 获取或创建联系方式记录，得到联系方式ID。
    2. 根据额外信息中的"昵称"键（如果存在）或整个额外信息JSON，检查线索是否已存在。
    3. 如果因"昵称"匹配而存在，则更新线索的"信息"字段。
    4. 如果因完整信息匹配而存在（无"昵称"时），则报冲突。
    5. 如果不存在，则创建新线索记录。
    6. 组装线索详情并返回。
    """
    整体开始时间 = time.time()
    当前操作时间 = datetime.now()  # 用于创建时间和更新时间
    应用日志器.info(
        f"[服务_上传新线索] 开始处理上传请求，联系方式内容: {请求.联系方式.内容}, 操作用户id: {操作用户id}"
    )

    try:
        # 步骤1: 获取或创建联系方式
        步骤开始时间 = time.time()
        联系方式数据库记录 = await 线索数据操作.获取或创建联系方式并返回完整数据(
            内容=请求.联系方式.内容,
            类型=请求.联系方式.类型 or "未知",
            记录来源=请求.联系方式.来源,
        )
        if not 联系方式数据库记录:
            错误日志器.error(
                f"[服务_上传新线索] 获取或创建联系方式失败，未返回有效数据。输入: {请求.联系方式}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="处理联系方式时关键数据未能获取",
            )
        联系方式id = 联系方式数据库记录["id"]
        应用日志器.info(
            f"[服务_上传新线索] 获取或创建联系方式完成，耗时: {time.time() - 步骤开始时间:.4f}秒, 联系方式ID: {联系方式id}"
        )

        额外信息_dict = 请求.额外信息  # 可能为 None
        规范化信息_json_str_for_creation: Optional[str] = None
        if 额外信息_dict is not None:
            try:
                # 确保用于创建和精确匹配的JSON是规范的（排序键等）
                规范化信息_json_str_for_creation = json.dumps(
                    额外信息_dict,
                    ensure_ascii=False,
                    sort_keys=True,
                    separators=(",", ":"),
                )
            except TypeError as e:
                错误日志器.error(
                    f"[服务_上传新线索] 额外信息序列化失败: {额外信息_dict}, 错误: {e}",
                    exc_info=True,
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"额外信息格式错误: {str(e)}",
                )

        # 步骤2 & 3 & 4: 条件化检查线索是否存在，并执行相应操作 (更新或准备创建)
        已存在线索id: Optional[int] = None
        执行创建操作 = True  # 默认需要创建，除非判断重复后更新或报错

        # 检查额外信息（请求体中的原始字典）中是否有'名称'键 (原为'昵称')
        名称存在于额外信息中 = (
            isinstance(额外信息_dict, dict) and "名称" in 额外信息_dict
        )  # 名称修改处

        应用日志器.info(
            f"[服务_上传新线索] 开始条件化查重。联系方式ID: {联系方式id}, 额外信息中是否含名称: {名称存在于额外信息中}"
        )  # 名称修改处
        步骤开始时间 = time.time()
        # 调用新的条件化查重函数，注意第三个参数的传递
        已存在线索id = await 线索数据操作.检查线索是否存在_条件化(
            联系方式id=联系方式id,
            额外信息=额外信息_dict or {},  # 传递原始字典，如果为None则使用空字典
            检查名称=名称存在于额外信息中,  # 如果名称在额外信息中，则按名称逻辑检查 (原为检查昵称) # 名称修改处
        )
        应用日志器.info(
            f"[服务_上传新线索] 检查线索是否存在_条件化 返回id: {已存在线索id}, 耗时: {time.time() - 步骤开始时间:.4f}秒"
        )

        新创建的线索ID_在此流程: Optional[int] = None
        最终线索数据用于响应: Dict[
            str, Any
        ] = {}  # 用于组装最后返回的 线索详情响应模型 的数据来源
        action_type_for_router: str = ""  # 初始化 action_type

        if 已存在线索id is not None:
            # 线索已存在，根据判断逻辑决定是更新还是报错
            执行创建操作 = False
            if 名称存在于额外信息中:  # 名称修改处
                # 按名称匹配到存在，需要更新 '信息' 字段 (合并逻辑) (原为按昵称)
                应用日志器.info(
                    f"[服务_上传新线索] 线索因名称匹配已存在 (ID: {已存在线索id})。准备合并更新信息字段。"
                )  # 名称修改处

                # 1. 读取旧信息
                旧线索_原始数据 = await 线索数据操作.根据id获取线索(已存在线索id)
                if not 旧线索_原始数据:
                    错误日志器.critical(
                        f"[服务_上传新线索] 严重错误：先前检测到存在的线索 (ID: {已存在线索id}) 在尝试读取进行合并更新时却找不到了！"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="更新线索信息时发生内部数据一致性错误",
                    )

                旧信息_json_str = 旧线索_原始数据.get("信息")
                旧信息_字典: Dict[str, Any] = {}
                if 旧信息_json_str:
                    try:
                        旧信息_字典 = json.loads(旧信息_json_str)
                        if not isinstance(旧信息_字典, dict):
                            应用日志器.warning(
                                f"[服务_上传新线索] 线索 (ID: {已存在线索id}) 的旧信息反序列化后不是字典: {旧信息_字典}，将视为空字典处理。"
                            )
                            旧信息_字典 = {}
                    except json.JSONDecodeError:
                        错误日志器.error(
                            f"[服务_上传新线索] 线索 (ID: {已存在线索id}) 的旧信息JSON解析失败: {旧信息_json_str}，将视为空字典处理。"
                        )
                        旧信息_字典 = {}  # 解析失败则视为空字典，避免影响合并

                # 2. 准备新信息 (额外信息_dict 已在前面从请求中获取)
                新信息_字典 = 额外信息_dict if isinstance(额外信息_dict, dict) else {}

                # 3. 合并信息 (新信息覆盖旧信息中的同名键)
                合并后信息_字典 = 旧信息_字典.copy()  # 从旧信息开始
                合并后信息_字典.update(
                    新信息_字典
                )  # 新信息中的键值对更新（或添加）到合并字典中

                # 4. 序列化合并后信息
                try:
                    合并后信息_json_str = json.dumps(
                        合并后信息_字典,
                        ensure_ascii=False,
                        sort_keys=True,
                        separators=(",", ":"),
                    )
                except TypeError as e:
                    错误日志器.error(
                        f"[服务_上传新线索] 合并后的额外信息序列化失败: {合并后信息_字典}, 错误: {e}",
                        exc_info=True,
                    )
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"处理后的额外信息格式错误: {str(e)}",
                    )

                # 5. 准备更新
                更新内容 = {"信息": 合并后信息_json_str, "更新用户": 操作用户id}

                # 添加诊断日志
                错误日志器.critical(
                    f"DEBUG_TYPE_ERROR: 即将调用 更新线索数据. 线索ID: {已存在线索id}, 操作用户id: {操作用户id}"
                )
                错误日志器.critical(f"DEBUG_TYPE_ERROR: 更新内容 字典: {更新内容}")
                try:
                    sig = inspect.signature(线索数据操作.更新线索信息)
                    错误日志器.critical(f"DEBUG_TYPE_ERROR: 目标函数签名: {sig}")
                except Exception as inspect_e:
                    错误日志器.critical(
                        f"DEBUG_TYPE_ERROR: 无法获取目标函数签名: {inspect_e}"
                    )

                # 6. 执行更新
                更新成功 = await 线索数据操作.更新线索信息(
                    线索ID=已存在线索id, 更新字段=更新内容
                )

                if not 更新成功:
                    错误日志器.warning(
                        f"[服务_上传新线索] 更新已存在线索 (ID: {已存在线索id}) 时，数据库报告0行受影响。将进一步检查原因。"
                    )  # 修改点：添加日志
                    # 重新查询线索，确认是数据未变还是记录消失
                    旧线索_再次检查 = await 线索数据操作.根据id获取线索(已存在线索id)
                    if not 旧线索_再次检查:
                        错误日志器.error(
                            f"[服务_上传新线索] 确认失败：之前匹配到的线索 (ID: {已存在线索id}) 在更新操作（0行影响）后检查时已不存在。可能发生并发删除。"
                        )
                        raise HTTPException(
                            status_code=status.HTTP_409_CONFLICT,
                            detail="目标线索在更新操作期间状态发生改变（可能已被删除），请刷新后重试",
                        )
                    else:
                        应用日志器.info(
                            f"[服务_上传新线索] 确认成功：线索 (ID: {已存在线索id}) 在更新操作（0行影响）后仍然存在。判定为信息未发生变化。"
                        )
                        action_type_for_router = "updated_by_name_match_no_change"  # 新增：标记操作类型 - 信息无变化
                        最终线索数据用于响应 = 旧线索_再次检查  # 使用再次检查获取的数据
                        新创建的线索ID_在此流程 = 已存在线索id  # 明确ID
                        # 注意：旧信息_字典 和 请求.额外信息 仍然是之前定义的，无需改变，它们会被用于填充响应的对应字段
                else:
                    应用日志器.info(
                        f"[服务_上传新线索] 线索 (ID: {已存在线索id}) 信息字段更新成功。准备获取更新后数据。"
                    )
                    # 获取更新后的完整线索数据用于响应
                    更新后线索_原始 = await 线索数据操作.根据id获取线索(已存在线索id)
                    if not 更新后线索_原始:
                        错误日志器.critical(
                            f"[服务_上传新线索] 严重错误：刚刚更新成功的线索 (ID: {已存在线索id}) 查询时却找不到了！"
                        )
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail="获取更新后线索详情时发生严重错误",
                        )
                    最终线索数据用于响应 = 更新后线索_原始
                    新创建的线索ID_在此流程 = (
                        已存在线索id  # 虽然是更新，但流程上是这条线索的ID
                    )
                    action_type_for_router = "updated_by_name_match"  # 确保更新成功时 action_type 正确设置 (原逻辑可能已覆盖，但这里显式保证)
            else:
                # 无名称，且按完整信息匹配到存在，报错
                重复提示 = f"线索已存在（联系方式ID: {联系方式id}，且详细信息完全匹配，未通过名称进行特定更新）。"
                应用日志器.warning(f"[服务_上传新线索] {重复提示}")
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT, detail=重复提示
                )

        if 执行创建操作:
            # 步骤5: 创建新线索
            应用日志器.info(
                "[服务_上传新线索] 线索不存在或无需因名称匹配而更新，准备创建新线索。"
            )
            步骤开始时间 = time.time()
            新创建的线索ID_在此流程 = await 线索数据操作.创建线索(
                联系方式id=联系方式id,
                信息_json=规范化信息_json_str_for_creation or "{}",  # 使用规范化的JSON字符串，如果为None则使用空对象
                线索来源_param=请求.线索来源,
                更新用户_param=操作用户id,
                创建时间_param=当前操作时间,
                更新时间_param=当前操作时间,
            )
            应用日志器.info(
                f"[服务_上传新线索] 成功创建新线索，ID: {新创建的线索ID_在此流程}, 耗时: {time.time() - 步骤开始时间:.4f}秒"
            )
            # 组装用于响应的数据字典（新创建的情况）
            最终线索数据用于响应 = {
                "id": 新创建的线索ID_在此流程,
                "联系方式id": 联系方式id,
                "信息": 额外信息_dict,  # 对于新创建，直接用请求中的dict (如果Pydantic模型能处理)
                "线索来源": 请求.线索来源,
                "更新用户": 操作用户id,
                "创建时间": 当前操作时间,
                "更新时间": 当前操作时间,
            }
            _action_type_for_router = "created"  # 新增：标记操作类型
        else:  # implies 已存在线索id is not None and (likely) 名称存在于额外信息中 (update path)
            # 如果 action_type_for_router 尚未被 "updated_by_name_match_no_change" 设置，
            # 并且也不是创建流程，那么它就是标准的 "updated_by_name_match"
            if (
                not action_type_for_router
            ):  # 仅当action_type未被内部逻辑（如no_change）设置时
                action_type_for_router = "updated_by_name_match"

        # 步骤6: 组装响应模型
        关联联系方式响应模型: Optional[联系方式详情模型] = None
        if 联系方式数据库记录:  # 复用之前获取的联系方式完整记录
            关联联系方式响应模型 = 联系方式详情模型.model_validate(
                {
                    "id": 联系方式数据库记录.get("id"),
                    "内容": 联系方式数据库记录.get("联系方式"),
                    "类型": 联系方式数据库记录.get("类型"),
                    "来源": 联系方式数据库记录.get("来源"),
                    "创建时间": 联系方式数据库记录.get("创建时间"),
                    "更新时间": 联系方式数据库记录.get("更新时间"),
                }
            )

        # 确保 最终线索数据用于响应 包含所有必要字段来构建 线索详情响应模型
        # 特别是，如果 `最终线索数据用于响应` 来自 `根据id获取线索` (更新流程)
        # 需要确保其 `信息` 字段是反序列化后的字典，而不是JSON字符串
        if isinstance(最终线索数据用于响应.get("信息"), str):
            try:
                最终线索数据用于响应["信息"] = json.loads(最终线索数据用于响应["信息"])
            except json.JSONDecodeError:
                错误日志器.error(
                    f"[服务_上传新线索] 从数据库获取的线索信息JSON解析失败: {最终线索数据用于响应.get('信息')}"
                )
                最终线索数据用于响应["信息"] = {
                    "error": "信息JSON损坏"
                }  # 或者None，取决于模型定义

        响应数据准备 = {
            **最终线索数据用于响应,  # 这应该包含了id, 联系方式id, (处理过的)信息, 线索来源, 更新用户, 创建时间, 更新时间
            "关联联系方式": 关联联系方式响应模型,
        }

        # 如果是更新操作，则填充 更新前信息 和 本次请求的应用信息
        if action_type_for_router == "updated_by_name_match":
            响应数据准备["更新前信息"] = (
                旧信息_字典  # 旧信息_字典 已在该分支前获取和定义
            )
            响应数据准备["本次请求的应用信息"] = (
                请求.额外信息
            )  # 请求.额外信息 即为本次用于更新的信息
        elif (
            action_type_for_router == "updated_by_name_match_no_change"
        ):  # 新增对 no_change 的处理
            响应数据准备["更新前信息"] = 旧信息_字典  # 即使无变化，也返回这些信息
            响应数据准备["本次请求的应用信息"] = 请求.额外信息

        步骤开始时间 = time.time()
        try:
            详细响应 = 线索详情响应模型.model_validate(响应数据准备)
            应用日志器.info(
                f"[服务_上传新线索] 组装线索详情响应完成，耗时: {time.time() - 步骤开始时间:.4f}秒"
            )
        except Exception as e_validate:
            错误日志器.error(
                f"[服务_上传新线索] 组装响应模型校验失败: {响应数据准备}, 错误: {e_validate}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="组装新线索详情时发生内部校验错误",
            )

        应用日志器.info(
            f"[服务_上传新线索] 总处理完成，总耗时: {time.time() - 整体开始时间:.4f}秒。返回线索ID: {新创建的线索ID_在此流程}"
        )
        return {"action_type": action_type_for_router, "lead_details": 详细响应}

    except HTTPException:
        raise
    except Exception as e:
        错误日志器.error(
            f"[服务_上传新线索] 处理失败。请求数据: {请求.model_dump_json(indent=2)}，错误: {str(e)}",
            exc_info=True,
        )
        应用日志器.error(
            f"[服务_上传新线索] 发生未知错误，总耗时: {time.time() - 整体开始时间:.4f}秒"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传新线索时发生内部错误: {str(e)}",
        )


async def 服务_获取线索详情(线索id_param: int) -> 线索详情响应模型:
    """
    服务层核心逻辑：获取单个线索的详细信息。
    1. 从数据层获取原始线索数据。
    2. 如果线索有关联的联系方式，也获取联系方式数据。
    3. 反序列化线索信息中的JSON数据。
    4. 组装成 线索详情响应模型。
    """
    应用日志器.info(f"[服务_获取线索详情] 开始获取线索ID: {线索id_param} 的详情")
    原始线索数据 = await 线索数据操作.根据id获取线索(线索id_param)

    if not 原始线索数据:
        错误日志器.warning(f"[服务_获取线索详情] 未找到线索ID: {线索id_param}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"ID为 {线索id_param} 的线索不存在",
        )

    应用日志器.debug(f"[服务_获取线索详情] 获取到原始线索数据: {原始线索数据}")

    关联联系方式模型: Optional[联系方式详情模型] = None
    if 原始线索数据.get("联系方式id") is not None:
        原始联系方式数据 = await 线索数据操作.根据ID查询联系方式(
            原始线索数据["联系方式id"]
        )
        if 原始联系方式数据:
            应用日志器.debug(
                f"[服务_获取线索详情] 获取到关联联系方式数据: {原始联系方式数据}"
            )
            # 数据转换: 将从数据库获取的字段名映射到模型期望的字段名
            转换后的联系方式数据 = {
                "id": 原始联系方式数据.get("id"),
                "内容": 原始联系方式数据.get("联系方式"),
                "类型": 原始联系方式数据.get("类型"),
                "来源": 原始联系方式数据.get("来源"),
                "创建时间": 原始联系方式数据.get("创建时间"),
                "更新时间": 原始联系方式数据.get("更新时间"),
            }
            关联联系方式模型 = 联系方式详情模型.model_validate(转换后的联系方式数据)
        else:
            应用日志器.warning(
                f"[服务_获取线索详情] 线索 {线索id_param} 的联系方式ID {原始线索数据['联系方式id']} 在联系方式表中未找到记录"
            )

    信息_dict: Optional[Dict[str, Any]] = None
    if 原始线索数据.get("信息"):
        try:
            信息_dict = json.loads(原始线索数据["信息"])
            应用日志器.debug(f"[服务_获取线索详情] 线索信息JSON已解析: {信息_dict}")
        except json.JSONDecodeError as e:
            错误日志器.error(
                f"[服务_获取线索详情] 线索ID: {线索id_param} 的信息字段JSON解析失败: {原始线索数据['信息']}, 错误: {e}",
                exc_info=True,
            )
            信息_dict = {
                "error": "信息字段JSON格式损坏",
                "raw_data": 原始线索数据["信息"],
            }

    响应数据准备 = {**原始线索数据, "信息": 信息_dict, "关联联系方式": 关联联系方式模型}

    try:
        详细响应 = 线索详情响应模型.model_validate(响应数据准备)
        应用日志器.info(
            f"[服务_获取线索详情] 成功组装线索ID: {线索id_param} 的详情响应"
        )
        return 详细响应
    except Exception as e:
        错误日志器.error(
            f"[服务_获取线索详情] 组装线索详情响应模型失败。准备数据: {响应数据准备}, 错误: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取线索详情时发生内部错误",
        )


async def 服务_更新指定线索(
    请求: 线索更新请求模型, 操作用户id: int
) -> 线索详情响应模型:
    """
    服务层核心逻辑：更新指定ID的线索。
    1. 检查线索是否存在。
    2. 如果请求中提供了联系方式信息，则获取或创建新的联系方式ID。
    3. 构建需要更新到数据库的字段字典。
    4. 调用数据层执行更新。
    5. 获取更新后的线索详情并返回。
    """
    应用日志器.info(
        f"[服务_更新指定线索] 开始处理更新请求，线索ID: {请求.线索id}, 操作用户id: {操作用户id}"
    )

    # 1. 确保线索存在 (服务_获取线索详情内部会检查并抛出404)
    # 我们也可以先单独查一下，避免不必要的后续处理，或者依赖详情函数抛异常
    现有线索_原始 = await 线索数据操作.根据id获取线索(请求.线索id)
    if not 现有线索_原始:
        错误日志器.warning(
            f"[服务_更新指定线索] 尝试更新不存在的线索，ID: {请求.线索id}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"ID为 {请求.线索id} 的线索不存在，无法更新",
        )

    应用日志器.debug(f"[服务_更新指定线索] 找到待更新的原始线索数据: {现有线索_原始}")

    更新内容字典: Dict[str, Any] = {}

    # 2. 处理联系方式更新
    if 请求.联系方式:
        应用日志器.debug(f"[服务_更新指定线索] 请求中包含联系方式更新: {请求.联系方式}")
        联系方式数据 = await 线索数据操作.获取或创建联系方式并返回完整数据(
            内容=请求.联系方式.内容,
            类型=请求.联系方式.类型 or "未知",
            记录来源=请求.联系方式.来源,
        )
        if 联系方式数据:
            新联系方式id = 联系方式数据.get("id")
            更新内容字典["联系方式id"] = 新联系方式id
        else:
            应用日志器.error("[服务_更新指定线索] 获取或创建联系方式失败")
            raise HTTPException(status_code=500, detail="联系方式处理失败")
        应用日志器.info(
            f"[服务_更新指定线索] 线索ID {请求.线索id} 的联系方式将更新为ID: {新联系方式id}"
        )

    # 3. 处理其他字段更新
    if (
        请求.线索来源 is not None
    ):  # 允许设置为空字符串，但不允许None跳过（除非模型定义了默认值）
        更新内容字典["线索来源"] = 请求.线索来源

    if 请求.额外信息 is not None:  # 如果提供了额外信息（即使是空字典），则更新
        try:
            更新内容字典["信息"] = json.dumps(
                请求.额外信息, ensure_ascii=False, sort_keys=True, separators=(",", ":")
            )
            应用日志器.debug(
                f"[服务_更新指定线索] 线索ID {请求.线索id} 的额外信息将更新为: {更新内容字典['信息']}"
            )
        except TypeError as e:
            错误日志器.error(
                f"[服务_更新指定线索] 更新的额外信息序列化失败: {请求.额外信息}, 错误: {e}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"额外信息格式错误，无法序列化: {str(e)}",
            )

    # 更新操作人和更新时间是必须的
    更新内容字典["更新用户"] = 操作用户id
    # 更新时间由数据层的 更新线索数据 函数自动处理

    if not 更新内容字典:
        应用日志器.info(
            f"[服务_更新指定线索] 线索ID: {请求.线索id} 没有提供任何可更新的字段内容（除了操作用户）。"
        )
        # 即使没有其他字段更新，至少更新了操作用户和更新时间，所以继续执行
        # 如果业务逻辑是"无实际内容变化则不调数据库"，这里可以提前返回详情
        # return await 服务_获取线索详情(请求.线索id)

    try:
        # 4. 调用数据层执行更新
        更新成功 = await 线索数据操作.更新线索信息(线索ID=请求.线索id, 更新字段=更新内容字典)

        if 更新成功:
            应用日志器.info(
                f"[服务_更新指定线索] 线索ID: {请求.线索id} 数据层更新成功。"
            )
        else:
            # 这可能意味着记录不存在（已被其他请求删除）或数据没有变化，数据层返回False
            应用日志器.warning(
                f"[服务_更新指定线索] 线索ID: {请求.线索id} 数据层更新未产生影响。可能记录已不存在或数据无变化。"
            )
            # 重新检查记录是否存在，以返回准确的错误信息
            重新检查 = await 线索数据操作.根据id获取线索(请求.线索id)
            if not 重新检查:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"尝试更新的线索ID {请求.线索id} 在操作过程中已不存在",
                )
            # 如果记录存在但没有更新，说明提供的数据可能与现有数据相同

        # 5. 获取更新后的线索详情并返回
        return await 服务_获取线索详情(请求.线索id)

    except HTTPException:  # 直接重新抛出已处理的HTTPException
        raise
    except Exception as e:
        错误日志器.error(
            f"[服务_更新指定线索] 处理失败。线索ID: {请求.线索id}, 请求: {请求.model_dump_json(indent=2)}, 错误: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新线索时发生内部错误",
        )


async def 服务_获取线索列表(
    请求: 获取线索列表请求模型, 用户权限: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:  # 返回类型对应 线索列表响应数据 模型
    """
    服务层核心逻辑：获取线索列表，支持分页和筛选。
    1. 调用数据层获取原始线索数据列表和总数。
    2. 对每条原始线索数据：
        a. 反序列化'信息'字段 (JSON字符串 -> Dict)。
        b. 根据数据层返回的关联联系方式信息，准备联系方式简略文本。
        c. 组装成 线索列表项响应模型。
    3. 返回包含列表和分页信息的字典。
    """
    应用日志器.info(
        f"[服务_获取线索列表] 开始处理列表获取请求: {请求.model_dump(exclude_none=True)}"
    )
    try:
        原始记录列表, 总记录数 = await 线索数据操作.分页查询线索列表(
            页码=请求.页码,
            每页数量=请求.每页数量,
            筛选_联系方式=请求.筛选_联系方式,
            筛选_线索来源=请求.筛选_线索来源,
            起始id=请求.起始id,
            筛选_信息值=请求.筛选_信息值
        )
        应用日志器.debug(
            f"[服务_获取线索列表] 数据层返回 {len(原始记录列表)} 条记录，总数: {总记录数}"
        )

        处理后列表: List[线索列表项响应模型] = []
        if 原始记录列表:
            for 原始记录 in 原始记录列表:
                信息_dict: Optional[Dict[str, Any]] = None
                if 原始记录.get("信息"):
                    try:
                        信息_dict = json.loads(原始记录["信息"])
                    except json.JSONDecodeError:
                        错误日志器.warning(
                            f"[服务_获取线索列表] 线索ID {原始记录.get('id')} 的信息字段JSON解析失败。列表项中将设为None。"
                        )
                        # 在列表视图中，如果JSON损坏，通常不应阻止整个列表的显示
                        # 可以选择设为None，或一个特定的错误提示，但模型需要能接受

                # 从数据层返回的 `关联_联系方式内容` 等字段准备联系方式简略信息
                联系方式简略文本: Optional[str] = None
                关联内容 = 原始记录.get("关联_联系方式内容")
                # 关联类型 = 原始记录.get("关联_联系方式类型")  # 暂时不使用
                if 关联内容:
                    # 联系方式简略文本格式: 类型: 内容前15字符...
                    # 这里可以加入更复杂的打码逻辑，如果需要的话
                    pass

                列表项数据准备 = {
                    **原始记录,  # id, 线索来源, 更新用户, 创建时间, 更新时间, 联系方式id
                    "信息": 信息_dict,
                    "联系方式简略": 联系方式简略文本,
                }
                处理后列表.append(线索列表项响应模型.model_validate(列表项数据准备))

        应用日志器.info(
            f"[服务_获取线索列表] 成功处理线索列表，返回 {len(处理后列表)} 条。"
        )

        # 处理数据隐私保护
        if 用户权限:
            总数隐私处理 = 数据隐私服务实例.处理总数隐私(总记录数, 用户权限)
            分页信息处理 = 数据隐私服务实例.处理分页信息(
                总记录数, 请求.页码, 请求.每页数量, 用户权限
            )

            return {
                "列表": 处理后列表,
                "总数": 总记录数,  # 保留实际总数用于分页计算
                "显示总数": 总数隐私处理["display_total"],  # 模糊化显示总数
                "页码": 请求.页码,
                "每页数量": 请求.每页数量,
                "隐私信息": {
                    "is_fuzzy": 总数隐私处理["is_fuzzy"],
                    "privacy_level": 总数隐私处理["privacy_level"],
                    "user_type": 总数隐私处理["user_type"],
                },
                "分页信息": 分页信息处理,
                "数据规模提示": 数据隐私服务实例.生成数据规模提示(总记录数, "线索"),
            }
        else:
            # 未提供用户权限时，使用默认隐私保护
            return {
                "列表": 处理后列表,
                "总数": 总记录数,
                "显示总数": "数据较多",
                "页码": 请求.页码,
                "每页数量": 请求.每页数量,
                "隐私信息": {
                    "is_fuzzy": True,
                    "privacy_level": "high",
                    "user_type": "未知",
                },
            }

    except Exception as e:
        错误日志器.error(
            f"[服务_获取线索列表] 处理失败。请求: {请求.model_dump_json(indent=2)}，错误: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取线索列表时发生内部错误",
        )


# 后续其他服务函数将在此添加...
